<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockFlow Pro - Next-Gen Inventory Intelligence</title>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    
    <style>
        :root {
            /* Premium Color System - Blue Purple Gradient */
            --primary: #667eea;
            --primary-hover: #5b73e8;
            --primary-light: #9bb5ff;
            --primary-dark: #4c63d2;
            --primary-glow: rgba(102, 126, 234, 0.3);
            
            --secondary: #764ba2;
            --secondary-hover: #6a4190;
            --secondary-light: #a085ca;
            --secondary-dark: #5d3280;
            
            --accent: #ff6b9d;
            --accent-hover: #ff5a92;
            --accent-glow: rgba(255, 107, 157, 0.3);
            
            --success: #00d4aa;
            --success-hover: #00c49a;
            --success-glow: rgba(0, 212, 170, 0.3);
            
            --warning: #ffb800;
            --warning-hover: #e6a600;
            --warning-glow: rgba(255, 184, 0, 0.3);
            
            --error: #ff5757;
            --error-hover: #ff4757;
            --error-glow: rgba(255, 87, 87, 0.3);
            
            /* Premium Neutral Palette */
            --white: #ffffff;
            --gray-25: #fefefe;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --gray-950: #020617;
            
            /* Premium Theme Variables */
            --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-glass: rgba(255, 255, 255, 0.25);
            --bg-glass-strong: rgba(255, 255, 255, 0.4);
            --bg-card: rgba(255, 255, 255, 0.8);
            --bg-elevated: rgba(255, 255, 255, 0.95);
            
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-tertiary: #64748b;
            --text-quaternary: #94a3b8;
            
            --border-primary: rgba(226, 232, 240, 0.8);
            --border-secondary: rgba(203, 213, 225, 0.6);
            --border-focus: rgba(102, 126, 234, 0.5);
            
            /* Next-Level Shadows */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
            --shadow-glow: 0 0 20px var(--primary-glow);
            --shadow-glow-strong: 0 0 40px var(--primary-glow);
            
            /* Premium Gradients */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            --gradient-success: linear-gradient(135deg, #00d4aa 0%, #36d399 100%);
            --gradient-warning: linear-gradient(135deg, #ffb800 0%, #fcd34d 100%);
            --gradient-error: linear-gradient(135deg, #ff5757 0%, #f87171 100%);
            --gradient-accent: linear-gradient(135deg, #ff6b9d 0%, #667eea 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.2) 100%);
            --gradient-mesh: 
                radial-gradient(at 40% 20%, hsla(228,100%,74%,0.12) 0px, transparent 50%), 
                radial-gradient(at 80% 0%, hsla(189,100%,56%,0.12) 0px, transparent 50%), 
                radial-gradient(at 0% 50%, hsla(355,100%,93%,0.12) 0px, transparent 50%),
                radial-gradient(at 40% 80%, hsla(240,100%,70%,0.08) 0px, transparent 50%);
            
            /* Spacing System */
            --space-0: 0px;
            --space-1: 4px;
            --space-2: 8px;
            --space-3: 12px;
            --space-4: 16px;
            --space-5: 20px;
            --space-6: 24px;
            --space-8: 32px;
            --space-10: 40px;
            --space-12: 48px;
            --space-16: 64px;
            --space-20: 80px;
            --space-24: 96px;
            --space-32: 128px;
            
            /* Radius System */
            --radius-sm: 8px;
            --radius: 12px;
            --radius-md: 16px;
            --radius-lg: 20px;
            --radius-xl: 24px;
            --radius-2xl: 28px;
            --radius-3xl: 32px;
            --radius-full: 9999px;
            
            /* Typography */
            --font-mono: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-numbers: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            
            /* Animations */
            --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
            --ease-elastic: cubic-bezier(0.68, -0.6, 0.32, 1.6);
        }

        [data-theme="dark"] {
            /* Ultra-Modern Dark Color System */
            --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            --bg-secondary: #0f172a;
            --bg-tertiary: #1e293b;
            --bg-glass: rgba(15, 23, 42, 0.6);
            --bg-glass-strong: rgba(15, 23, 42, 0.8);
            --bg-card: rgba(30, 41, 59, 0.6);
            --bg-elevated: rgba(51, 65, 85, 0.8);
            
            --text-primary: #f8fafc;
            --text-secondary: #e2e8f0;
            --text-tertiary: #94a3b8;
            --text-quaternary: #64748b;
            
            --border-primary: rgba(248, 250, 252, 0.12);
            --border-secondary: rgba(248, 250, 252, 0.06);
            --border-focus: rgba(102, 126, 234, 0.4);
            
            /* Dark Mode Gradients */
            --gradient-mesh: 
                radial-gradient(at 20% 80%, hsla(240,100%,70%,0.08) 0px, transparent 50%),
                radial-gradient(at 80% 20%, hsla(270,100%,70%,0.08) 0px, transparent 50%),
                radial-gradient(at 40% 40%, hsla(200,100%,70%,0.08) 0px, transparent 50%);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        *::before,
        *::after {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-mesh);
            z-index: -1;
            animation: meshAnimation 20s ease-in-out infinite;
        }

        @keyframes meshAnimation {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 0.8; }
        }

        /* Numbers specific styling - Modern & Professional */
        .number, .stat-value, .chart-number {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
            font-weight: 800 !important;
            font-feature-settings: "tnum", "lnum";
            letter-spacing: -0.02em;
            line-height: 1.1;
        }

        /* Premium Layout */
        .app {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        .sidebar {
            width: 280px;
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border-right: 1px solid var(--border-primary);
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 50;
            transform: translateX(-100%);
            transition: all 0.5s var(--ease-spring);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar.open {
            transform: translateX(0);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 1px;
            background: linear-gradient(180deg, transparent 0%, var(--primary-glow) 50%, transparent 100%);
        }

        .sidebar-header {
            padding: var(--space-8);
            border-bottom: 1px solid var(--border-primary);
            background: var(--bg-glass);
            position: relative;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            cursor: pointer;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: var(--gradient-primary);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 900;
            font-size: 20px;
            box-shadow: var(--shadow-xl), var(--shadow-glow);
            position: relative;
            overflow: hidden;
            transition: all 0.4s var(--ease-spring);
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            transform: translateX(-100%) skewX(-15deg);
            transition: transform 0.6s ease;
        }

        .logo:hover .logo-icon::before {
            transform: translateX(200%) skewX(-15deg);
        }

        .logo:hover .logo-icon {
            transform: scale(1.05) rotate(-5deg);
            box-shadow: var(--shadow-2xl), var(--shadow-glow-strong);
        }

        .logo-text {
            font-size: 24px;
            font-weight: 900;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: 1;
        }

        .nav {
            padding: var(--space-8) var(--space-6);
        }

        .nav-section {
            margin-bottom: var(--space-10);
        }

        .nav-section-title {
            font-size: 10px;
            font-weight: 800;
            color: var(--text-quaternary);
            text-transform: uppercase;
            letter-spacing: 0.2em;
            margin-bottom: var(--space-6);
            padding: 0 var(--space-4);
            position: relative;
        }

        .nav-section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 2px;
            height: 2px;
            background: var(--primary);
            border-radius: 50%;
            transform: translateY(-50%);
            box-shadow: 0 0 4px var(--primary-glow);
        }

        .nav-item {
            margin-bottom: var(--space-2);
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            padding: var(--space-4) var(--space-5);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-xl);
            transition: all 0.3s var(--ease-spring);
            font-weight: 600;
            font-size: 15px;
            position: relative;
            overflow: hidden;
            border: 1px solid transparent;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            inset: 0;
            background: var(--gradient-glass);
            opacity: 0;
            transition: all 0.3s var(--ease-spring);
            border-radius: var(--radius-xl);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--gradient-primary);
            border-radius: 0 2px 2px 0;
            transform: scaleY(0);
            transition: transform 0.3s var(--ease-spring);
        }

        .nav-link:hover {
            color: var(--text-primary);
            transform: translateX(8px);
            border-color: var(--border-focus);
            box-shadow: var(--shadow-lg);
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link:hover::after {
            transform: scaleY(1);
        }

        .nav-link:hover .nav-icon {
            transform: scale(1.1);
        }

        .nav-link.active {
            background: var(--bg-glass-strong);
            color: var(--text-primary);
            border-color: var(--border-focus);
            box-shadow: var(--shadow-xl);
            transform: translateX(8px);
        }

        .nav-link.active::after {
            transform: scaleY(1);
        }

        .nav-link.active .nav-icon {
            transform: scale(1.05);
        }

        .nav-icon {
            width: 22px;
            height: 22px;
            flex-shrink: 0;
            position: relative;
            z-index: 2;
            transition: transform 0.3s var(--ease-spring);
        }

        .nav-link span {
            position: relative;
            z-index: 2;
        }

        .nav-badge {
            background: var(--gradient-error);
            color: white;
            font-size: 9px;
            font-weight: 900;
            padding: 3px 8px;
            border-radius: var(--radius-full);
            margin-left: auto;
            box-shadow: var(--shadow-md);
            position: relative;
            z-index: 2;
            letter-spacing: 0.05em;
            animation: pulse-badge 2s infinite;
        }

        @keyframes pulse-badge {
            0%, 100% {
                box-shadow: var(--shadow-md), 0 0 0 0 var(--error-glow);
            }
            50% {
                box-shadow: var(--shadow-md), 0 0 0 4px rgba(255, 87, 87, 0);
            }
        }

        /* Main Content */
        .main {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 0;
            min-height: 100vh;
            width: 100%;
        }

        .header {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border-bottom: 1px solid var(--border-primary);
            padding: 0 var(--space-8);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80px;
            position: sticky;
            top: 0;
            z-index: 40;
            box-shadow: var(--shadow-lg);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-6);
        }

        .menu-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            border: 1px solid var(--border-primary);
            background: var(--bg-glass);
            color: var(--text-secondary);
            border-radius: var(--radius-xl);
            cursor: pointer;
            transition: all 0.3s var(--ease-spring);
            backdrop-filter: blur(12px);
        }

        .menu-button:hover {
            background: var(--bg-elevated);
            color: var(--primary);
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
            border-color: var(--border-focus);
        }

        .page-title {
            font-size: 28px;
            font-weight: 900;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .theme-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            border: 1px solid var(--border-primary);
            background: var(--bg-glass);
            color: var(--text-secondary);
            border-radius: var(--radius-xl);
            cursor: pointer;
            transition: all 0.3s var(--ease-spring);
            backdrop-filter: blur(12px);
        }

        .theme-toggle:hover {
            background: var(--bg-elevated);
            color: var(--primary);
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
            border-color: var(--border-focus);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-2) var(--space-4);
            border: 1px solid var(--border-primary);
            background: var(--bg-glass);
            border-radius: var(--radius-2xl);
            cursor: pointer;
            transition: all 0.3s var(--ease-spring);
            backdrop-filter: blur(12px);
        }

        .user-menu:hover {
            background: var(--bg-elevated);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: var(--border-focus);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: var(--gradient-primary);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 800;
            font-size: 14px;
            box-shadow: var(--shadow-md);
        }

        .user-name {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .content {
            flex: 1;
            padding: var(--space-8);
            max-width: 1600px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        /* Page sections */
        .page-section {
            display: none;
            animation: slideInPage 0.6s var(--ease-spring);
        }

        .page-section.active {
            display: block;
        }

        @keyframes slideInPage {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Premium Cards */
        .card {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            overflow: hidden;
            box-shadow: var(--shadow-2xl);
            transition: all 0.4s var(--ease-spring);
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            border-color: var(--border-focus);
        }

        .card-header {
            padding: var(--space-8);
            border-bottom: 1px solid var(--border-secondary);
            background: var(--bg-glass);
        }

        .card-title {
            font-size: 22px;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: var(--space-1);
            letter-spacing: -0.01em;
        }

        .card-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .card-content {
            padding: var(--space-8);
        }

        /* Premium Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-12);
        }

        .stat-card {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            transition: all 0.4s var(--ease-spring);
            position: relative;
            overflow: hidden;
            group: hover;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.4s var(--ease-spring);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            border-color: var(--border-focus);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-8);
        }

        .stat-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-secondary);
            letter-spacing: -0.01em;
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-xl);
            transition: all 0.3s var(--ease-spring);
        }

        .stat-icon::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.3) 0%, transparent 100%);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .stat-card:hover .stat-icon::before {
            transform: translateX(100%);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-value {
            font-size: 48px;
            font-weight: 900;
            color: var(--text-primary);
            margin-bottom: var(--space-3);
            line-height: 1;
            letter-spacing: -0.03em;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-feature-settings: "tnum", "lnum";
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 15px;
            font-weight: 600;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(12px);
            border: 1px solid transparent;
            transition: all 0.3s var(--ease-spring);
        }

        .stat-change.positive {
            color: var(--success);
            background: rgba(0, 212, 170, 0.15);
            border-color: rgba(0, 212, 170, 0.3);
        }

        .stat-change.negative {
            color: var(--error);
            background: rgba(255, 87, 87, 0.15);
            border-color: rgba(255, 87, 87, 0.3);
        }

        .stat-change.neutral {
            color: var(--text-tertiary);
            background: rgba(100, 116, 139, 0.15);
            border-color: rgba(100, 116, 139, 0.3);
        }

        /* Premium Charts */
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .chart-card {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            min-height: 480px;
            box-shadow: var(--shadow-2xl);
            transition: all 0.4s var(--ease-spring);
        }

        .chart-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
        }

        .chart-header {
            margin-bottom: var(--space-8);
        }

        .chart-title {
            font-size: 24px;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
            letter-spacing: -0.01em;
        }

        .chart-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .chart-container {
            position: relative;
            height: 360px;
            width: 100%;
        }

        /* Premium Tables */
        .table-card {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            overflow: hidden;
            box-shadow: var(--shadow-2xl);
            transition: all 0.4s var(--ease-spring);
        }

        .table-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
        }

        .table-header {
            padding: var(--space-8);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--space-6);
            flex-wrap: wrap;
            background: var(--bg-glass);
        }

        .table-title {
            font-size: 22px;
            font-weight: 800;
            color: var(--text-primary);
            flex: 1;
            min-width: 200px;
            letter-spacing: -0.01em;
        }

        .table-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            flex-wrap: wrap;
        }

        .search-input {
            background: var(--bg-glass);
            backdrop-filter: blur(12px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-4) var(--space-5);
            color: var(--text-primary);
            font-size: 15px;
            min-width: 280px;
            transition: all 0.3s var(--ease-spring);
            font-weight: 500;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px var(--primary-glow);
            transform: scale(1.02);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 15px;
        }

        .table th {
            background: var(--bg-glass);
            backdrop-filter: blur(12px);
            padding: var(--space-5) var(--space-6);
            text-align: left;
            font-size: 13px;
            font-weight: 800;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.15em;
            border-bottom: 1px solid var(--border-secondary);
        }

        .table td {
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-secondary);
            color: var(--text-secondary);
            font-size: 15px;
            font-weight: 500;
        }

        /* Consistent Table Font Styling */
        .table td:nth-child(1), 
        .table td:nth-child(2) {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            color: var(--text-tertiary);
        }

        .table td:nth-child(3) {
            font-weight: 600;
            color: var(--text-primary);
        }

        .table td:nth-child(4) {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 16px;
            letter-spacing: -0.01em;
        }

        .table td:nth-child(5) {
            font-weight: 500;
        }

        /* Stock In/Out Quantity Colors */
        .table td[data-type="stock-in"] {
            color: var(--success) !important;
        }

        .table td[data-type="stock-out"] {
            color: var(--warning) !important;
        }

        /* Inventory Table Specific Styling */
        .table.inventory-table td:nth-child(1) {
            font-weight: 600;
            color: var(--text-primary);
            font-family: 'Plus Jakarta Sans', sans-serif;
        }

        .table.inventory-table td:nth-child(2) {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            color: var(--text-tertiary);
        }

        .table.inventory-table td:nth-child(4),
        .table.inventory-table td:nth-child(5),
        .table.inventory-table td:nth-child(6),
        .table.inventory-table td:nth-child(7) {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            text-align: center;
            letter-spacing: -0.01em;
        }

        .table.inventory-table td:nth-child(7) {
            color: var(--text-primary);
            font-weight: 800;
        }

        /* Report History Table Specific Styling */
        .table td:nth-child(2) {
            font-weight: 600;
            color: var(--text-primary);
        }

        .table tbody tr {
            transition: all 0.3s var(--ease-spring);
            position: relative;
        }

        .table tbody tr:hover {
            background: var(--bg-glass);
            transform: scale(1.01);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Premium Status Badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-xl);
            font-size: 13px;
            font-weight: 700;
            text-transform: capitalize;
            backdrop-filter: blur(12px);
            box-shadow: var(--shadow-sm);
            border: 1px solid transparent;
            transition: all 0.3s var(--ease-spring);
        }

        .status-badge.safe {
            background: rgba(0, 212, 170, 0.15);
            color: var(--success);
            border-color: rgba(0, 212, 170, 0.3);
        }

        .status-badge.warning {
            background: rgba(255, 184, 0, 0.15);
            color: var(--warning);
            border-color: rgba(255, 184, 0, 0.3);
        }

        .status-badge.danger {
            background: rgba(255, 87, 87, 0.15);
            color: var(--error);
            border-color: rgba(255, 87, 87, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: var(--radius-full);
            background: currentColor;
            box-shadow: 0 0 8px currentColor;
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Premium Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-4) var(--space-6);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            font-size: 15px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s var(--ease-spring);
            white-space: nowrap;
            background: var(--bg-glass);
            backdrop-filter: blur(12px);
            color: var(--text-secondary);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.15) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .btn:hover {
            background: var(--bg-elevated);
            color: var(--text-primary);
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--shadow-xl);
            border-color: var(--border-focus);
        }

        .btn:hover::before {
            opacity: 1;
        }

        .btn:active {
            transform: translateY(0) scale(1);
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: var(--gradient-primary);
            border-color: var(--primary);
            color: white;
            box-shadow: var(--shadow-lg), var(--shadow-glow);
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: translateY(-3px) scale(1.02);
            box-shadow: var(--shadow-2xl), var(--shadow-glow-strong);
            border-color: var(--primary-light);
        }

        .btn-accent {
            background: var(--gradient-accent);
            border-color: var(--accent);
            color: white;
            box-shadow: var(--shadow-lg), var(--accent-glow);
        }

        .btn-accent:hover {
            background: var(--gradient-accent);
            transform: translateY(-3px) scale(1.02);
            box-shadow: var(--shadow-2xl), var(--accent-glow);
        }

        .btn-ghost {
            border: none;
            background: transparent;
            backdrop-filter: none;
        }

        .btn-ghost:hover {
            background: var(--bg-glass);
            color: var(--primary);
            backdrop-filter: blur(12px);
        }

        /* Premium Forms */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .form-label {
            font-size: 15px;
            font-weight: 700;
            color: var(--text-primary);
            letter-spacing: -0.01em;
        }

        .form-input, .form-select {
            background: var(--bg-glass);
            backdrop-filter: blur(12px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-5);
            color: var(--text-primary);
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s var(--ease-spring);
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px var(--primary-glow);
            transform: scale(1.02);
        }

        /* Settings Toggle Styles */
        .settings-toggle {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .settings-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-300);
            transition: 0.4s var(--ease-spring);
            border-radius: var(--radius-full);
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s var(--ease-spring);
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
        }

        input:checked + .slider {
            background: var(--gradient-primary);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Premium Modal */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(12px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s var(--ease-spring);
        }

        .modal.open {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            padding: var(--space-12);
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-2xl);
            transform: scale(0.8) translateY(40px);
            transition: all 0.4s var(--ease-spring);
        }

        .modal.open .modal-content {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-10);
        }

        .modal-title {
            font-size: 28px;
            font-weight: 900;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
        }

        .modal-close {
            width: 44px;
            height: 44px;
            border: none;
            background: var(--bg-glass);
            border-radius: var(--radius-full);
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s var(--ease-spring);
        }

        .modal-close:hover {
            background: var(--error);
            color: white;
            transform: rotate(90deg) scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        /* Premium FAB */
        .fab {
            position: fixed;
            bottom: var(--space-8);
            right: var(--space-8);
            width: 72px;
            height: 72px;
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--radius-full);
            color: white;
            font-size: 28px;
            cursor: pointer;
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            transition: all 0.4s var(--ease-spring);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fab:hover {
            transform: scale(1.15) rotate(90deg);
            box-shadow: var(--shadow-2xl), var(--shadow-glow-strong);
        }

        .fab:active {
            transform: scale(1.1) rotate(90deg);
        }

        /* Premium Insights */
        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .insight-card {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            border-left: 4px solid var(--primary);
            transition: all 0.4s var(--ease-spring);
            position: relative;
            overflow: hidden;
        }

        .insight-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, var(--primary-glow) 0%, transparent 70%);
        }

        .insight-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
            border-color: var(--primary);
        }

        .insight-icon {
            width: 36px;
            height: 36px;
            color: var(--primary);
            margin-bottom: var(--space-6);
        }

        .insight-title {
            font-size: 20px;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            letter-spacing: -0.01em;
        }

        .insight-description {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* AI Insights Specific Styles */
        .ai-insights {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
            backdrop-filter: blur(12px);
        }

        .ai-title {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 24px;
            font-weight: 800;
            color: var(--primary);
            margin-bottom: var(--space-6);
        }

        .ai-robot {
            width: 32px;
            height: 32px;
            background: var(--gradient-primary);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-6px); }
        }

        .ai-insight {
            background: var(--bg-glass);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            margin-bottom: var(--space-4);
            font-size: 16px;
            line-height: 1.6;
        }

        .ai-insight:last-child {
            margin-bottom: 0;
        }

        .ai-insight strong {
            color: var(--text-primary);
        }

        /* Premium Activity */
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .activity-item {
            display: flex;
            align-items: start;
            gap: var(--space-5);
            padding: var(--space-6);
            border-radius: var(--radius-2xl);
            transition: all 0.3s var(--ease-spring);
            backdrop-filter: blur(12px);
            border: 1px solid transparent;
        }

        .activity-item:hover {
            background: var(--bg-glass);
            transform: translateX(8px);
            border-color: var(--border-focus);
        }

        .activity-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-size: 16px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
            letter-spacing: -0.01em;
        }

        .activity-meta {
            font-size: 14px;
            color: var(--text-tertiary);
            font-family: var(--font-mono);
        }

        /* Success Message */
        .success-message {
            background: rgba(0, 212, 170, 0.15);
            color: var(--success);
            padding: var(--space-5) var(--space-6);
            border-radius: var(--radius-2xl);
            margin-bottom: var(--space-8);
            font-weight: 700;
            display: none;
            border: 1px solid rgba(0, 212, 170, 0.3);
            backdrop-filter: blur(12px);
        }

        /* WhatsApp Configuration */
        .whatsapp-config {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(37, 211, 102, 0.05) 100%);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: var(--radius-3xl);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
            backdrop-filter: blur(12px);
        }

        .whatsapp-title {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 20px;
            font-weight: 800;
            color: #25d366;
            margin-bottom: var(--space-6);
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
        }

        /* Premium Login */
        .login-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-8);
            background: var(--bg-secondary);
            position: relative;
        }

        .login-page::before {
            content: '';
            position: absolute;
            inset: 0;
            background: var(--gradient-mesh);
            animation: meshAnimation 20s ease-in-out infinite;
        }

        .login-card {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(40px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-3xl);
            padding: var(--space-16);
            width: 100%;
            max-width: 520px;
            box-shadow: var(--shadow-2xl);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--space-12);
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: var(--radius-3xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 900;
            font-size: 32px;
            margin: 0 auto var(--space-6);
            box-shadow: var(--shadow-2xl), var(--shadow-glow);
        }

        .login-title {
            font-size: 32px;
            font-weight: 900;
            color: var(--text-primary);
            margin-bottom: var(--space-3);
            letter-spacing: -0.02em;
        }

        .login-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
        }

        /* Overlay */
        .overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 40;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s var(--ease-spring);
        }

        .overlay.open {
            opacity: 1;
            visibility: visible;
        }

        /* Premium Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: var(--shadow-xl), 0 0 0 0 var(--primary-glow);
            }
            50% {
                box-shadow: var(--shadow-xl), 0 0 0 20px rgba(102, 126, 234, 0);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .slide-in {
            animation: slideIn 0.6s var(--ease-spring);
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: var(--space-6);
            }
            
            /* Make dashboard chart section responsive */
            .content > div:nth-child(3) {
                grid-template-columns: 1fr !important;
            }
        }

        @media (max-width: 768px) {
            .main {
                margin-left: 0;
            }
            
            .content {
                padding: var(--space-5);
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }
            
            /* Mobile dashboard adjustments */
            .content > div:nth-child(3) {
                grid-template-columns: 1fr !important;
                gap: var(--space-6) !important;
            }
            
            .table-header {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-4);
            }
            
            .search-input {
                min-width: auto;
                width: 100%;
            }
            
            .form-grid, .config-grid {
                grid-template-columns: 1fr;
                gap: var(--space-5);
            }
            
            .header {
                padding: 0 var(--space-5);
                height: 72px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .user-name {
                display: none;
            }
            
            .login-card {
                padding: var(--space-10);
                margin: var(--space-5);
            }

            .modal-content {
                padding: var(--space-8);
                margin: var(--space-5);
            }

            .fab {
                bottom: var(--space-5);
                right: var(--space-5);
                width: 64px;
                height: 64px;
            }

            .insights-grid {
                grid-template-columns: 1fr;
            }
            
            /* Hide hero section on mobile to save space */
            .content > div:first-child {
                display: none;
            }
        }

        @media (min-width: 1200px) {
            .sidebar {
                position: fixed;
                transform: translateX(0);
            }
            
            .main {
                margin-left: 280px;
                width: calc(100% - 280px);
            }
            
            .menu-button {
                display: none;
            }
            
            .overlay {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .table th:nth-child(n+6),
            .table td:nth-child(n+6) {
                display: none;
            }
            
            .stat-card {
                padding: var(--space-6);
            }
            
            .stat-value {
                font-size: 32px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="page-section active">
        <div class="login-page">
            <div class="login-card slide-in">
                <div class="login-header">
                    <div class="login-logo">SF</div>
                    <h1 class="login-title">Welcome back</h1>
                    <p class="login-subtitle">Sign in to your StockFlow Pro account</p>
                </div>
                
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-input" placeholder="Enter your email" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-input" placeholder="Enter your password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%; justify-content: center; margin-top: var(--space-8);">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10,17 15,12 10,7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        Sign in to StockFlow Pro
                    </button>
                </form>
                
                <div style="text-align: center; margin-top: var(--space-8); padding-top: var(--space-6); border-top: 1px solid var(--border-primary);">
                    <p style="color: var(--text-secondary); margin-bottom: var(--space-4);">Don't have an account?</p>
                    <button class="btn" id="showRegisterBtn" style="width: 100%; justify-content: center;">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <line x1="19" y1="8" x2="19" y2="14"></line>
                            <line x1="22" y1="11" x2="16" y2="11"></line>
                        </svg>
                        Create New Account
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Page - SIMPLIFIED & MODERN -->
    <div id="registerPage" class="page-section">
        <div class="login-page">
            <div class="login-card slide-in">
                <div class="login-header">
                    <div class="login-logo">SF</div>
                    <h1 class="login-title">Join StockFlow Pro</h1>
                    <p class="login-subtitle">Create your account and start managing inventory smartly</p>
                </div>
                
                <form id="registerForm">
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input type="text" class="form-input" name="fullName" placeholder="Enter your full name" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-input" name="email" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-input" name="password" placeholder="Create a strong password" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" class="form-input" name="confirmPassword" placeholder="Confirm your password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%; justify-content: center; margin-top: var(--space-8);">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <line x1="19" y1="8" x2="19" y2="14"></line>
                            <line x1="22" y1="11" x2="16" y2="11"></line>
                        </svg>
                        Create Account
                    </button>
                </form>
                
                <div style="text-align: center; margin-top: var(--space-8); padding-top: var(--space-6); border-top: 1px solid var(--border-primary);">
                    <p style="color: var(--text-secondary); margin-bottom: var(--space-4);">Already have an account?</p>
                    <button class="btn" id="showLoginBtn" style="width: 100%; justify-content: center;">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10,17 15,12 10,7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        Sign In Instead
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="page-section">
        <div class="app">
            <!-- Overlay -->
            <div class="overlay" id="overlay"></div>
            
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <div class="logo">
                        <div class="logo-icon">SF</div>
                        <span class="logo-text">StockFlow Pro</span>
                    </div>
                </div>
                
                <nav class="nav">
                    <div class="nav-section">
                        <div class="nav-section-title">Main</div>
                        <div class="nav-item">
                            <a href="#" class="nav-link active" data-page="dashboard">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="14" width="7" height="7"></rect>
                                    <rect x="3" y="14" width="7" height="7"></rect>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="inventory">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                </svg>
                                <span>Inventory</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">Transactions</div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="stock-in">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17l6-6 6 6"></path>
                                    <path d="M13 11v10"></path>
                                </svg>
                                <span>Stock In</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="stock-out">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M17 7l-6 6-6-6"></path>
                                    <path d="M11 13V3"></path>
                                </svg>
                                <span>Stock Out</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">Analytics</div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="reports">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M3 3v5h5M3 21l7-7 4 4 7-7M14 8h7v7"></path>
                                </svg>
                                <span>Reports & Analytics</span>
                                <span class="nav-badge">AI</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="activity">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                                </svg>
                                <span>Live Activity</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">Automation</div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="whatsapp">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                </svg>
                                <span>WhatsApp Reports</span>
                                <span class="nav-badge">Auto</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">System</div>
                        <div class="nav-item">
                            <a href="#" class="nav-link" data-page="settings">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
                                </svg>
                                <span>Settings</span>
                            </a>
                        </div>
                        <div class="nav-item" style="margin-top: var(--space-8);">
                            <a href="#" class="nav-link" onclick="logout()">
                                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                    <polyline points="16,17 21,12 16,7"></polyline>
                                    <line x1="21" y1="12" x2="9" y2="12"></line>
                                </svg>
                                <span>Sign out</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="main">
                <!-- Header -->
                <header class="header">
                    <div class="header-left">
                        <button class="menu-button" id="menuButton">
                            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="3" y1="6" x2="21" y2="6"></line>
                                <line x1="3" y1="12" x2="21" y2="12"></line>
                                <line x1="3" y1="18" x2="21" y2="18"></line>
                            </svg>
                        </button>
                        <h1 class="page-title" id="pageTitle">Dashboard</h1>
                    </div>
                    
                    <div class="header-right">
                        <button class="theme-toggle" id="themeToggle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="5"></circle>
                                <line x1="12" y1="1" x2="12" y2="3"></line>
                                <line x1="12" y1="21" x2="12" y2="23"></line>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                <line x1="1" y1="12" x2="3" y2="12"></line>
                                <line x1="21" y1="12" x2="23" y2="12"></line>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                            </svg>
                        </button>
                        <div class="user-menu">
                            <div class="user-avatar" id="userAvatar">AD</div>
                            <span class="user-name" id="userName">Admin</span>
                        </div>
                    </div>
                </header>

                <!-- Dashboard Content -->
                <div id="dashboardContent" class="content page-section active">
                    <!-- Modern Hero Section -->
                    <div style="background: var(--gradient-primary); border-radius: var(--radius-3xl); padding: var(--space-12); margin-bottom: var(--space-12); color: white; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; right: 0; width: 200px; height: 200px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>
                        <div style="position: relative; z-index: 2;">
                            <h2 style="font-size: 36px; font-weight: 900; margin-bottom: var(--space-4); font-family: 'Plus Jakarta Sans';">Welcome back, Admin! 👋</h2>
                            <p style="font-size: 18px; opacity: 0.9; margin-bottom: var(--space-8);">Your inventory is performing excellently today. Here's what's happening in real-time.</p>
                            <div style="display: flex; gap: var(--space-6); flex-wrap: wrap;">
                                <div style="background: rgba(255,255,255,0.2); padding: var(--space-4) var(--space-6); border-radius: var(--radius-xl); backdrop-filter: blur(12px);">
                                    <div style="font-size: 28px; font-weight: 900; font-family: 'Inter', sans-serif; letter-spacing: -0.02em;">98.7%</div>
                                    <div style="font-size: 14px; opacity: 0.8;">System Uptime</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.2); padding: var(--space-4) var(--space-6); border-radius: var(--radius-xl); backdrop-filter: blur(12px);">
                                    <div style="font-size: 28px; font-weight: 900; font-family: 'Inter', sans-serif; letter-spacing: -0.02em;">2.3s</div>
                                    <div style="font-size: 14px; opacity: 0.8;">Avg Response</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.2); padding: var(--space-4) var(--space-6); border-radius: var(--radius-xl); backdrop-filter: blur(12px);">
                                    <div style="font-size: 28px; font-weight: 900; font-family: 'Inter', sans-serif; letter-spacing: -0.02em;">24/7</div>
                                    <div style="font-size: 14px; opacity: 0.8;">Auto Monitoring</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Stats Grid -->
                    <div class="stats-grid">
                        <div class="stat-card slide-in" style="border-left: 4px solid var(--primary);">
                            <div class="stat-header">
                                <span class="stat-title">Total Products</span>
                                <div class="stat-icon" style="background: var(--gradient-primary);">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="stat-value number" id="totalProducts">286</div>
                            <div class="stat-change positive">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                    <polyline points="17 6 23 6 23 12"></polyline>
                                </svg>
                                +12% this month
                            </div>
                        </div>

                        <div class="stat-card slide-in" style="animation-delay: 0.1s; border-left: 4px solid var(--success);">
                            <div class="stat-header">
                                <span class="stat-title">Stock In Today</span>
                                <div class="stat-icon" style="background: var(--gradient-success);">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M7 17l6-6 6 6"></path>
                                        <path d="M13 11v10"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="stat-value number" id="stockIn">147</div>
                            <div class="stat-change positive">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                    <polyline points="17 6 23 6 23 12"></polyline>
                                </svg>
                                +23% from yesterday
                            </div>
                        </div>

                        <div class="stat-card slide-in" style="animation-delay: 0.2s; border-left: 4px solid var(--warning);">
                            <div class="stat-header">
                                <span class="stat-title">Stock Out Today</span>
                                <div class="stat-icon" style="background: var(--gradient-warning);">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M17 7l-6 6-6-6"></path>
                                        <path d="M11 13V3"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="stat-value number" id="stockOut">89</div>
                            <div class="stat-change negative">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                                    <polyline points="17 18 23 18 23 12"></polyline>
                                </svg>
                                -8% from yesterday
                            </div>
                        </div>

                        <div class="stat-card slide-in" style="animation-delay: 0.3s; border-left: 4px solid var(--error);">
                            <div class="stat-header">
                                <span class="stat-title">Critical Stock</span>
                                <div class="stat-icon" style="background: var(--gradient-error);">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                        <line x1="12" y1="9" x2="12" y2="13"></line>
                                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                    </svg>
                                </div>
                            </div>
                            <div class="stat-value number" id="criticalStock">7</div>
                            <div class="stat-change neutral">
                                Immediate attention needed
                            </div>
                        </div>
                    </div>

                    <!-- Premium Charts Section -->
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--space-8); margin-bottom: var(--space-12);">
                        <!-- Main Chart -->
                        <div class="chart-card slide-in" style="animation-delay: 0.4s;">
                            <div class="chart-header">
                                <h3 class="chart-title">📈 Inventory Performance Analytics</h3>
                                <p class="chart-subtitle">Real-time insights and predictive analytics for the last 30 days</p>
                            </div>
                            <div class="chart-container">
                                <canvas id="stockChart"></canvas>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div style="display: grid; gap: var(--space-6);">
                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-6); border-left: 4px solid var(--accent);">
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-4);">
                                    <div style="width: 40px; height: 40px; background: var(--gradient-accent); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white;">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                                            <path d="M21 3v5h-5"></path>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                                            <path d="M3 21v-5h5"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div style="font-weight: 700; color: var(--text-primary);">Turnover Rate</div>
                                        <div style="font-size: 13px; color: var(--text-tertiary);">Monthly average</div>
                                    </div>
                                </div>
                                <div style="font-size: 36px; font-weight: 900; color: var(--text-primary); font-family: 'Inter', sans-serif; letter-spacing: -0.02em;">4.2x</div>
                                <div style="font-size: 14px; color: var(--success); font-weight: 600;">+18% vs last month</div>
                            </div>

                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-6); border-left: 4px solid var(--success);">
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-4);">
                                    <div style="width: 40px; height: 40px; background: var(--gradient-success); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white;">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12,6 12,12 16,14"></polyline>
                                        </svg>
                                    </div>
                                    <div>
                                        <div style="font-weight: 700; color: var(--text-primary);">Avg Days to Sell</div>
                                        <div style="font-size: 13px; color: var(--text-tertiary);">Per product</div>
                                    </div>
                                </div>
                                <div style="font-size: 36px; font-weight: 900; color: var(--text-primary); font-family: 'Inter', sans-serif; letter-spacing: -0.02em;">12.5</div>
                                <div style="font-size: 14px; color: var(--success); font-weight: 600;">2.3 days faster</div>
                            </div>

                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-6); border-left: 4px solid var(--primary);">
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-4);">
                                    <div style="width: 40px; height: 40px; background: var(--gradient-primary); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white;">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                        </svg>
                                    </div>
                                    <div>
                                        <div style="font-weight: 700; color: var(--text-primary);">Stock Efficiency</div>
                                        <div style="font-size: 13px; color: var(--text-tertiary);">Overall health</div>
                                    </div>
                                </div>
                                <div style="font-size: 36px; font-weight: 900; color: var(--text-primary); font-family: 'Inter', sans-serif; letter-spacing: -0.02em;">87%</div>
                                <div style="font-size: 14px; color: var(--success); font-weight: 600;">+5% improvement</div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Insights -->
                    <div class="insights-grid">
                        <div class="insight-card slide-in" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%); border-left: 4px solid var(--primary);">
                            <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                <div style="width: 48px; height: 48px; background: var(--gradient-primary); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🤖</div>
                                <div>
                                    <h3 class="insight-title">AI-Powered Stock Analysis</h3>
                                    <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Real-time predictions</p>
                                </div>
                            </div>
                            <p class="insight-description">Advanced machine learning algorithms predict demand patterns and optimize inventory levels. Get intelligent alerts before critical shortages occur.</p>
                        </div>
                        
                        <div class="insight-card slide-in" style="animation-delay: 0.2s; background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(37, 211, 102, 0.05) 100%); border-left: 4px solid var(--success);">
                            <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                <div style="width: 48px; height: 48px; background: var(--gradient-success); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">📱</div>
                                <div>
                                    <h3 class="insight-title">Automated WhatsApp Reports</h3>
                                    <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">24/7 monitoring</p>
                                </div>
                            </div>
                            <p class="insight-description">Real-time inventory reports delivered directly to your WhatsApp. Configure custom schedules and recipient groups for seamless team coordination.</p>
                        </div>
                    </div>

                    <!-- Recent Activity Preview -->
                    <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); margin-top: var(--space-8);">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-8);">
                            <div>
                                <h3 style="font-size: 20px; font-weight: 800; color: var(--text-primary); margin-bottom: var(--space-2);">🔴 Live Activity</h3>
                                <p style="color: var(--text-secondary);">Real-time inventory movements</p>
                            </div>
                            <button class="btn" onclick="showPage('activity')" style="background: var(--gradient-primary); color: white; border: none;">
                                View All Activity
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="9,18 15,12 9,6"></polyline>
                                </svg>
                            </button>
                        </div>
                        
                        <div style="display: grid; gap: var(--space-4);" id="dashboardActivityPreview">
                            <div style="display: flex; align-items: center; gap: var(--space-4); padding: var(--space-4); background: var(--bg-glass); border-radius: var(--radius-xl);">
                                <div style="width: 40px; height: 40px; background: var(--gradient-success); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white;">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M7 17l6-6 6 6"></path>
                                        <path d="M13 11v10"></path>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary);">Stock In: iPhone 15 Pro Max</div>
                                    <div style="font-size: 13px; color: var(--text-tertiary); font-family: var(--font-mono);">+45 units • SKU: IP15PM001 • 2 minutes ago</div>
                                </div>
                            </div>
                            
                            <div style="display: flex; align-items: center; gap: var(--space-4); padding: var(--space-4); background: var(--bg-glass); border-radius: var(--radius-xl);">
                                <div style="width: 40px; height: 40px; background: var(--gradient-warning); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white;">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M17 7l-6 6-6-6"></path>
                                        <path d="M11 13V3"></path>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary);">Stock Out: MacBook Pro M3</div>
                                    <div style="font-size: 13px; color: var(--text-tertiary); font-family: var(--font-mono);">-12 units • SKU: MBP24M3002 • 15 minutes ago</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Content -->
                <div id="inventoryContent" class="content page-section">
                    <!-- Premium FAB -->
                    <button class="fab pulse" id="addProductFab" title="Add New Product">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </button>

                    <div class="table-card">
                        <div class="table-header">
                            <h3 class="table-title">Inventory Management</h3>
                            <div class="table-actions">
                                <input type="search" class="search-input" placeholder="Search products, SKU, or category..." id="productSearch">
                                <button class="btn">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="7,10 12,15 17,10"></polyline>
                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                    </svg>
                                    Export Data
                                </button>
                                <button class="btn btn-primary" id="addProductBtn">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    Add Product
                                </button>
                            </div>
                        </div>
                        
                        <table class="table inventory-table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Category</th>
                                    <th>Initial</th>
                                    <th>In</th>
                                    <th>Out</th>
                                    <th>Current</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="inventoryTableBody">
                                <tr>
                                    <td>iPhone 15 Pro Max</td>
                                    <td>IP15PM001</td>
                                    <td>Electronics</td>
                                    <td>150</td>
                                    <td>125</td>
                                    <td>89</td>
                                    <td>186</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Safe</span></td>
                                    <td>
                                        <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MacBook Pro M3</td>
                                    <td>MBP24M3002</td>
                                    <td>Electronics</td>
                                    <td>80</td>
                                    <td>45</td>
                                    <td>32</td>
                                    <td>93</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Safe</span></td>
                                    <td>
                                        <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>AirPods Pro Gen 3</td>
                                    <td>APP24G3003</td>
                                    <td>Accessories</td>
                                    <td>300</td>
                                    <td>180</td>
                                    <td>165</td>
                                    <td>15</td>
                                    <td><span class="status-badge warning"><span class="status-dot"></span>Low</span></td>
                                    <td>
                                        <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Magic Keyboard</td>
                                    <td>MKB24004</td>
                                    <td>Accessories</td>
                                    <td>120</td>
                                    <td>35</td>
                                    <td>58</td>
                                    <td>3</td>
                                    <td><span class="status-badge danger"><span class="status-dot"></span>Critical</span></td>
                                    <td>
                                        <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Studio Display</td>
                                    <td>SD24005</td>
                                    <td>Electronics</td>
                                    <td>50</td>
                                    <td>28</td>
                                    <td>19</td>
                                    <td>59</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Safe</span></td>
                                    <td>
                                        <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Premium Modal -->
                    <div class="modal" id="addProductModal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2 class="modal-title">Add New Product</h2>
                                <button class="modal-close" id="closeModal">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="success-message" id="successMessage">
                                ✅ Product added successfully to inventory!
                            </div>
                            
                            <form id="addProductForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">Product Name</label>
                                        <input type="text" class="form-input" name="productName" placeholder="Enter product name" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">SKU Code</label>
                                        <input type="text" class="form-input" name="productCode" placeholder="e.g., PRD24006" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Category</label>
                                        <select class="form-input" name="category" required>
                                            <option value="">Select category</option>
                                            <option value="Electronics">Electronics</option>
                                            <option value="Accessories">Accessories</option>
                                            <option value="Components">Components</option>
                                            <option value="Software">Software</option>
                                            <option value="Others">Others</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Initial Stock</label>
                                        <input type="number" class="form-input" name="initialStock" placeholder="0" min="0" required>
                                    </div>
                                </div>
                                
                                <div style="display: flex; gap: var(--space-5); margin-top: var(--space-12);">
                                    <button type="button" class="btn" id="cancelAdd" style="flex: 1;">
                                        Cancel
                                    </button>
                                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        Add Product
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Stock In Content -->
                <div id="stockInContent" class="content page-section">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Add Stock In</h3>
                            <p class="card-subtitle">Record incoming inventory items with automated tracking</p>
                        </div>
                        <div class="card-content">
                            <form id="stockInForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">Product SKU</label>
                                        <select class="form-input" id="stockInProductCode" required>
                                            <option value="">Select product SKU</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Product Name</label>
                                        <input type="text" class="form-input" id="stockInProductName" placeholder="Auto-filled from SKU" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Quantity</label>
                                        <input type="number" class="form-input" id="stockInQuantity" placeholder="0" min="1" required>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                    Record Stock In
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Stock In -->
                    <div class="table-card" style="margin-top: var(--space-12);">
                        <div class="table-header">
                            <h3 class="table-title">Recent Stock In Transactions</h3>
                            <input type="search" class="search-input" placeholder="Search transactions...">
                        </div>
                        
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>SKU</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="stockInTableBody">
                                <tr>
                                    <td>Jun 10, 2025 14:30</td>
                                    <td>IP15PM001</td>
                                    <td>iPhone 15 Pro Max</td>
                                    <td data-type="stock-in">+45</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 10, 2025 11:15</td>
                                    <td>MBP24M3002</td>
                                    <td>MacBook Pro M3</td>
                                    <td data-type="stock-in">+25</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 9, 2025 16:45</td>
                                    <td>APP24G3003</td>
                                    <td>AirPods Pro Gen 3</td>
                                    <td data-type="stock-in">+80</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Stock Out Content -->
                <div id="stockOutContent" class="content page-section">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Record Stock Out</h3>
                            <p class="card-subtitle">Track outgoing inventory with real-time availability checks</p>
                        </div>
                        <div class="card-content">
                            <form id="stockOutForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">Product SKU</label>
                                        <select class="form-input" id="stockOutProductCode" required>
                                            <option value="">Select product SKU</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Product Name</label>
                                        <input type="text" class="form-input" id="stockOutProductName" placeholder="Auto-filled from SKU" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Quantity</label>
                                        <input type="number" class="form-input" id="stockOutQuantity" placeholder="0" min="1" required>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                    Record Stock Out
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Stock Out -->
                    <div class="table-card" style="margin-top: var(--space-12);">
                        <div class="table-header">
                            <h3 class="table-title">Recent Stock Out Transactions</h3>
                            <input type="search" class="search-input" placeholder="Search transactions...">
                        </div>
                        
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>SKU</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="stockOutTableBody">
                                <tr>
                                    <td>Jun 10, 2025 13:20</td>
                                    <td>IP15PM001</td>
                                    <td>iPhone 15 Pro Max</td>
                                    <td data-type="stock-out">-18</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 10, 2025 10:30</td>
                                    <td>MBP24M3002</td>
                                    <td>MacBook Pro M3</td>
                                    <td data-type="stock-out">-12</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 9, 2025 15:45</td>
                                    <td>APP24G3003</td>
                                    <td>AirPods Pro Gen 3</td>
                                    <td data-type="stock-out">-35</td>
                                    <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Reports & Analytics Content - COMPLETELY REDESIGNED -->
                <div id="reportsContent" class="content page-section">
                    <!-- Ultra-Modern Hero Section -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff6b9d 100%); border-radius: var(--radius-3xl); padding: var(--space-16); margin-bottom: var(--space-12); color: white; position: relative; overflow: hidden;">
                        <!-- Animated Background Elements -->
                        <div style="position: absolute; top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
                        <div style="position: absolute; bottom: -30px; left: -30px; width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
                        
                        <div style="position: relative; z-index: 2; text-align: center;">
                            <div style="display: inline-flex; align-items: center; gap: var(--space-4); background: rgba(255,255,255,0.2); padding: var(--space-3) var(--space-6); border-radius: var(--radius-full); margin-bottom: var(--space-8); backdrop-filter: blur(12px);">
                                <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #ff6b9d, #667eea); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 16px;">🚀</div>
                                <span style="font-weight: 700; font-size: 14px; text-transform: uppercase; letter-spacing: 0.1em;">AI-POWERED ANALYTICS</span>
                            </div>
                            
                            <h1 style="font-size: 56px; font-weight: 900; margin-bottom: var(--space-6); font-family: 'Plus Jakarta Sans'; line-height: 1.1; background: linear-gradient(45deg, #ffffff, #e2e8f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                Next-Gen Business Intelligence
                            </h1>
                            
                            <p style="font-size: 20px; opacity: 0.9; margin-bottom: var(--space-12); max-width: 600px; margin-left: auto; margin-right: auto; line-height: 1.6;">
                                Unlock powerful insights with advanced analytics, real-time forecasting, and AI-driven recommendations for your inventory management.
                            </p>
                            
                            <!-- Interactive Stats Row -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-6); max-width: 800px; margin: 0 auto;">
                                <div style="background: rgba(255,255,255,0.15); padding: var(--space-6) var(--space-8); border-radius: var(--radius-2xl); backdrop-filter: blur(16px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s var(--ease-spring); cursor: pointer;" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <div style="font-size: 36px; font-weight: 900; font-family: 'Inter', sans-serif; letter-spacing: -0.02em; margin-bottom: var(--space-2);">95.8%</div>
                                    <div style="font-size: 14px; opacity: 0.9; font-weight: 600;">AI Accuracy</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); padding: var(--space-6) var(--space-8); border-radius: var(--radius-2xl); backdrop-filter: blur(16px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s var(--ease-spring); cursor: pointer;" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <div style="font-size: 36px; font-weight: 900; font-family: 'Inter', sans-serif; letter-spacing: -0.02em; margin-bottom: var(--space-2);">Real-time</div>
                                    <div style="font-size: 14px; opacity: 0.9; font-weight: 600;">Data Processing</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); padding: var(--space-6) var(--space-8); border-radius: var(--radius-2xl); backdrop-filter: blur(16px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s var(--ease-spring); cursor: pointer;" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <div style="font-size: 36px; font-weight: 900; font-family: 'Inter', sans-serif; letter-spacing: -0.02em; margin-bottom: var(--space-2);">24/7</div>
                                    <div style="font-size: 14px; opacity: 0.9; font-weight: 600;">Monitoring</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced AI Insights with Modern Cards -->
                    <div style="margin-bottom: var(--space-16);">
                        <div style="text-align: center; margin-bottom: var(--space-12);">
                            <div style="display: inline-flex; align-items: center; gap: var(--space-3); background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%); padding: var(--space-3) var(--space-6); border-radius: var(--radius-full); margin-bottom: var(--space-6); border: 1px solid rgba(102, 126, 234, 0.2);">
                                <div style="width: 28px; height: 28px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 14px; color: white;">🤖</div>
                                <span style="font-weight: 700; font-size: 14px; color: var(--primary); text-transform: uppercase; letter-spacing: 0.1em;">AI Intelligence Hub</span>
                            </div>
                            <h2 style="font-size: 36px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-4);">Smart Business Insights</h2>
                            <p style="font-size: 18px; color: var(--text-secondary); max-width: 600px; margin: 0 auto;">AI-powered recommendations and alerts to optimize your inventory performance</p>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: var(--space-8);">
                            <!-- Critical Alert Card -->
                            <div style="background: linear-gradient(135deg, rgba(255, 87, 87, 0.1) 0%, rgba(255, 87, 87, 0.05) 100%); border: 1px solid rgba(255, 87, 87, 0.2); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='0 25px 50px -12px rgba(255, 87, 87, 0.25)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255, 87, 87, 0.1) 0%, transparent 70%);"></div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                    <div style="width: 48px; height: 48px; background: var(--gradient-error); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; animation: pulse 2s infinite;">🚨</div>
                                    <div>
                                        <h3 style="font-size: 18px; font-weight: 800; color: var(--error); margin-bottom: var(--space-1);">Critical Alert</h3>
                                        <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Immediate attention required</p>
                                    </div>
                                </div>
                                <p style="font-size: 16px; color: var(--text-primary); line-height: 1.6; margin-bottom: var(--space-6);">
                                    Magic Keyboard showing zero movement for 28 days with only 3 units remaining. Immediate action recommended: promotional pricing or bundle offers.
                                </p>
                                <button style="background: var(--gradient-error); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: var(--radius-xl); font-weight: 600; cursor: pointer; transition: all 0.3s var(--ease-spring);">Take Action</button>
                            </div>

                            <!-- High Demand Card -->
                            <div style="background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.05) 100%); border: 1px solid rgba(0, 212, 170, 0.2); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='0 25px 50px -12px rgba(0, 212, 170, 0.25)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle, rgba(0, 212, 170, 0.1) 0%, transparent 70%);"></div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                    <div style="width: 48px; height: 48px; background: var(--gradient-success); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🔥</div>
                                    <div>
                                        <h3 style="font-size: 18px; font-weight: 800; color: var(--success); margin-bottom: var(--space-1);">High Demand</h3>
                                        <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Trending upward</p>
                                    </div>
                                </div>
                                <p style="font-size: 16px; color: var(--text-primary); line-height: 1.6; margin-bottom: var(--space-6);">
                                    iPhone 15 Pro Max experiencing 240% increase in velocity. Current stock may not meet weekend demand - schedule immediate restock.
                                </p>
                                <button style="background: var(--gradient-success); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: var(--radius-xl); font-weight: 600; cursor: pointer; transition: all 0.3s var(--ease-spring);">Restock Now</button>
                            </div>

                            <!-- Optimization Card -->
                            <div style="background: linear-gradient(135deg, rgba(255, 184, 0, 0.1) 0%, rgba(255, 184, 0, 0.05) 100%); border: 1px solid rgba(255, 184, 0, 0.2); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='0 25px 50px -12px rgba(255, 184, 0, 0.25)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255, 184, 0, 0.1) 0%, transparent 70%);"></div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                    <div style="width: 48px; height: 48px; background: var(--gradient-warning); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">💡</div>
                                    <div>
                                        <h3 style="font-size: 18px; font-weight: 800; color: var(--warning); margin-bottom: var(--space-1);">Optimization</h3>
                                        <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Smart recommendation</p>
                                    </div>
                                </div>
                                <p style="font-size: 16px; color: var(--text-primary); line-height: 1.6; margin-bottom: var(--space-6);">
                                    AirPods Pro Gen 3 showing high velocity but low stock. Recommend increasing minimum stock level from 20 to 50 units.
                                </p>
                                <button style="background: var(--gradient-warning); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: var(--radius-xl); font-weight: 600; cursor: pointer; transition: all 0.3s var(--ease-spring);">Optimize</button>
                            </div>

                            <!-- Growth Opportunity Card -->
                            <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%); border: 1px solid rgba(102, 126, 234, 0.2); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='0 25px 50px -12px rgba(102, 126, 234, 0.25)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);"></div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                    <div style="width: 48px; height: 48px; background: var(--gradient-primary); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">📈</div>
                                    <div>
                                        <h3 style="font-size: 18px; font-weight: 800; color: var(--primary); margin-bottom: var(--space-1);">Growth Opportunity</h3>
                                        <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Expansion potential</p>
                                    </div>
                                </div>
                                <p style="font-size: 16px; color: var(--text-primary); line-height: 1.6; margin-bottom: var(--space-6);">
                                    Electronics category showing 15% growth trajectory. Consider expanding MacBook Pro variants based on current performance metrics.
                                </p>
                                <button style="background: var(--gradient-primary); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: var(--radius-xl); font-weight: 600; cursor: pointer; transition: all 0.3s var(--ease-spring);">Expand</button>
                            </div>
                        </div>
                    </div>

                    <!-- Revolutionary Analytics Dashboard -->
                    <div style="display: grid; grid-template-columns: 1.8fr 1.2fr; gap: var(--space-12); margin-bottom: var(--space-16);">
                        <!-- Mega Chart Card -->
                        <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px) saturate(180%); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-10); box-shadow: var(--shadow-2xl); position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--gradient-primary);"></div>
                            <div style="margin-bottom: var(--space-10);">
                                <div style="display: flex; align-items: center; justify-content: between; margin-bottom: var(--space-4);">
                                    <div>
                                        <h3 style="font-size: 28px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-2); display: flex; align-items: center; gap: var(--space-3);">
                                            <span style="font-size: 32px;">📊</span>
                                            Sales Performance Analytics
                                        </h3>
                                        <p style="font-size: 16px; color: var(--text-secondary);">Top performing products with AI-powered insights (30 days)</p>
                                    </div>
                                </div>
                                <div style="display: flex; gap: var(--space-4); flex-wrap: wrap;">
                                    <div style="background: rgba(0, 212, 170, 0.1); padding: var(--space-3) var(--space-5); border-radius: var(--radius-xl); border: 1px solid rgba(0, 212, 170, 0.2);">
                                        <span style="font-size: 12px; color: var(--success); font-weight: 700; text-transform: uppercase; letter-spacing: 0.1em;">Trending Up</span>
                                    </div>
                                    <div style="background: rgba(102, 126, 234, 0.1); padding: var(--space-3) var(--space-5); border-radius: var(--radius-xl); border: 1px solid rgba(102, 126, 234, 0.2);">
                                        <span style="font-size: 12px; color: var(--primary); font-weight: 700; text-transform: uppercase; letter-spacing: 0.1em;">AI Optimized</span>
                                    </div>
                                </div>
                            </div>
                            <div style="height: 400px; position: relative;">
                                <canvas id="topProductsChart" style="border-radius: var(--radius-xl);"></canvas>
                            </div>
                        </div>

                        <!-- Revolutionary Metrics Stack -->
                        <div style="display: grid; gap: var(--space-6);">
                            <!-- Revenue Metric -->
                            <div style="background: linear-gradient(135deg, var(--bg-glass-strong), var(--bg-elevated)); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); text-align: center; position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-success);"></div>
                                <div style="width: 80px; height: 80px; background: var(--gradient-success); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-6); color: white; font-size: 36px; box-shadow: var(--shadow-2xl); animation: pulse 3s infinite;">📈</div>
                                <div style="font-size: 42px; font-weight: 900; color: var(--text-primary); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-success); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">$47.2K</div>
                                <div style="font-size: 16px; color: var(--text-secondary); font-weight: 700; margin-bottom: var(--space-2);">Revenue This Month</div>
                                <div style="background: rgba(0, 212, 170, 0.1); color: var(--success); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 14px; font-weight: 700; display: inline-block;">+18.5% vs last month</div>
                            </div>

                            <!-- Accuracy Metric -->
                            <div style="background: linear-gradient(135deg, var(--bg-glass-strong), var(--bg-elevated)); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); text-align: center; position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-primary);"></div>
                                <div style="width: 80px; height: 80px; background: var(--gradient-primary); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-6); color: white; font-size: 36px; box-shadow: var(--shadow-2xl);">🎯</div>
                                <div style="font-size: 42px; font-weight: 900; color: var(--text-primary); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">96.2%</div>
                                <div style="font-size: 16px; color: var(--text-secondary); font-weight: 700; margin-bottom: var(--space-2);">Order Accuracy</div>
                                <div style="background: rgba(102, 126, 234, 0.1); color: var(--primary); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 14px; font-weight: 700; display: inline-block;">+2.1% improvement</div>
                            </div>

                            <!-- Speed Metric -->
                            <div style="background: linear-gradient(135deg, var(--bg-glass-strong), var(--bg-elevated)); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); text-align: center; position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-warning);"></div>
                                <div style="width: 80px; height: 80px; background: var(--gradient-warning); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-6); color: white; font-size: 36px; box-shadow: var(--shadow-2xl);">⚡</div>
                                <div style="font-size: 42px; font-weight: 900; color: var(--text-primary); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-warning); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">1.8 hrs</div>
                                <div style="font-size: 16px; color: var(--text-secondary); font-weight: 700; margin-bottom: var(--space-2);">Avg Fulfillment</div>
                                <div style="background: rgba(255, 184, 0, 0.1); color: var(--warning); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 14px; font-weight: 700; display: inline-block;">-0.3 hrs faster</div>
                            </div>
                        </div>
                    </div>

                    <!-- Ultra-Modern Performance Metrics -->
                    <div style="margin-bottom: var(--space-16);">
                        <div style="text-align: center; margin-bottom: var(--space-12);">
                            <h2 style="font-size: 36px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-4);">Performance Metrics</h2>
                            <p style="font-size: 18px; color: var(--text-secondary); max-width: 600px; margin: 0 auto;">Real-time KPIs and performance indicators for data-driven decisions</p>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--space-8);">
                            <!-- Turnover Metric -->
                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='var(--shadow-2xl), 0 0 40px rgba(255, 107, 157, 0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--gradient-accent);"></div>
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-8);">
                                    <span style="font-size: 16px; font-weight: 600; color: var(--text-secondary);">Inventory Turnover</span>
                                    <div style="width: 56px; height: 56px; background: var(--gradient-accent); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; box-shadow: var(--shadow-xl);">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                                            <path d="M21 3v5h-5"></path>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                                            <path d="M3 21v-5h5"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div style="font-size: 48px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-3); font-family: 'Inter', sans-serif;">4.2x</div>
                                <div style="background: rgba(255, 107, 157, 0.1); color: var(--accent); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 15px; font-weight: 600; display: inline-flex; align-items: center; gap: var(--space-2);">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                        <polyline points="17 6 23 6 23 12"></polyline>
                                    </svg>
                                    +18% vs last month
                                </div>
                            </div>

                            <!-- Days to Sell -->
                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='var(--shadow-2xl), 0 0 40px rgba(0, 212, 170, 0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--gradient-success);"></div>
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-8);">
                                    <span style="font-size: 16px; font-weight: 600; color: var(--text-secondary);">Avg Days to Sell</span>
                                    <div style="width: 56px; height: 56px; background: var(--gradient-success); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; box-shadow: var(--shadow-xl);">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12,6 12,12 16,14"></polyline>
                                        </svg>
                                    </div>
                                </div>
                                <div style="font-size: 48px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-3); font-family: 'Inter', sans-serif;">12.5</div>
                                <div style="background: rgba(0, 212, 170, 0.1); color: var(--success); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 15px; font-weight: 600; display: inline-flex; align-items: center; gap: var(--space-2);">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                        <polyline points="17 6 23 6 23 12"></polyline>
                                    </svg>
                                    2.3 days faster
                                </div>
                            </div>

                            <!-- Stock Efficiency -->
                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='var(--shadow-2xl), 0 0 40px rgba(102, 126, 234, 0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--gradient-primary);"></div>
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-8);">
                                    <span style="font-size: 16px; font-weight: 600; color: var(--text-secondary);">Stock Efficiency</span>
                                    <div style="width: 56px; height: 56px; background: var(--gradient-primary); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; box-shadow: var(--shadow-xl);">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                        </svg>
                                    </div>
                                </div>
                                <div style="font-size: 48px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-3); font-family: 'Inter', sans-serif;">87%</div>
                                <div style="background: rgba(102, 126, 234, 0.1); color: var(--primary); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 15px; font-weight: 600; display: inline-flex; align-items: center; gap: var(--space-2);">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                        <polyline points="17 6 23 6 23 12"></polyline>
                                    </svg>
                                    +5% improvement
                                </div>
                            </div>

                            <!-- Forecast Accuracy -->
                            <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-8); position: relative; overflow: hidden; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-12px)'; this.style.boxShadow='var(--shadow-2xl), 0 0 40px rgba(255, 184, 0, 0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow=''">
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--gradient-warning);"></div>
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-8);">
                                    <span style="font-size: 16px; font-weight: 600; color: var(--text-secondary);">Forecast Accuracy</span>
                                    <div style="width: 56px; height: 56px; background: var(--gradient-warning); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; box-shadow: var(--shadow-xl);">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M9 17H7A5 5 0 0 1 7 7h2m0 10v3m0-3h5a5 5 0 0 0 0-10H9m0 0V4m0 3v10m0-10H7a5 5 0 0 0 0 10h2V7Z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div style="font-size: 48px; font-weight: 900; color: var(--text-primary); margin-bottom: var(--space-3); font-family: 'Inter', sans-serif;">94.3%</div>
                                <div style="background: rgba(255, 184, 0, 0.1); color: var(--warning); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 15px; font-weight: 600; display: inline-flex; align-items: center; gap: var(--space-2);">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                        <polyline points="17 6 23 6 23 12"></polyline>
                                    </svg>
                                    +1.2% this quarter
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Revolutionary Data Tables -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-12); margin-bottom: var(--space-16);">
                        <!-- Deadstock Analysis -->
                        <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); overflow: hidden; box-shadow: var(--shadow-2xl); position: relative;">
                            <div style="background: linear-gradient(135deg, rgba(255, 87, 87, 0.1) 0%, rgba(255, 87, 87, 0.05) 100%); padding: var(--space-8); border-bottom: 1px solid var(--border-secondary);">
                                <div style="display: flex; align-items: center; justify-content: between;">
                                    <div style="display: flex; align-items: center; gap: var(--space-4);">
                                        <div style="width: 48px; height: 48px; background: var(--gradient-error); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">💀</div>
                                        <div>
                                            <h3 style="font-size: 20px; font-weight: 800; color: var(--text-primary); margin-bottom: var(--space-1);">Deadstock Analysis</h3>
                                            <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Critical inventory insights</p>
                                        </div>
                                    </div>
                                    <button style="background: var(--gradient-error); color: white; border: none; padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 13px; font-weight: 600; cursor: pointer;">Export</button>
                                </div>
                            </div>
                            
                            <div style="padding: var(--space-6);">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="border-bottom: 1px solid var(--border-secondary);">
                                            <th style="padding: var(--space-4); text-align: left; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Product</th>
                                            <th style="padding: var(--space-4); text-align: center; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Days</th>
                                            <th style="padding: var(--space-4); text-align: center; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Stock</th>
                                            <th style="padding: var(--space-4); text-align: center; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="border-bottom: 1px solid var(--border-secondary); transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                            <td style="padding: var(--space-4); font-weight: 600; color: var(--text-primary);">Magic Keyboard</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700; color: var(--error);">28</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700;">3</td>
                                            <td style="padding: var(--space-4); text-align: center;"><span style="background: rgba(255, 87, 87, 0.1); color: var(--error); padding: var(--space-1) var(--space-3); border-radius: var(--radius-xl); font-size: 12px; font-weight: 700;">Urgent</span></td>
                                        </tr>
                                        <tr style="border-bottom: 1px solid var(--border-secondary); transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                            <td style="padding: var(--space-4); font-weight: 600; color: var(--text-primary);">Studio Display</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700; color: var(--warning);">15</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700;">59</td>
                                            <td style="padding: var(--space-4); text-align: center;"><span style="background: rgba(255, 184, 0, 0.1); color: var(--warning); padding: var(--space-1) var(--space-3); border-radius: var(--radius-xl); font-size: 12px; font-weight: 700;">Monitor</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Top Performers -->
                        <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); overflow: hidden; box-shadow: var(--shadow-2xl); position: relative;">
                            <div style="background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.05) 100%); padding: var(--space-8); border-bottom: 1px solid var(--border-secondary);">
                                <div style="display: flex; align-items: center; justify-content: between;">
                                    <div style="display: flex; align-items: center; gap: var(--space-4);">
                                        <div style="width: 48px; height: 48px; background: var(--gradient-success); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🏆</div>
                                        <div>
                                            <h3 style="font-size: 20px; font-weight: 800; color: var(--text-primary); margin-bottom: var(--space-1);">Top Performers</h3>
                                            <p style="font-size: 14px; color: var(--text-tertiary); margin: 0;">Best selling products</p>
                                        </div>
                                    </div>
                                    <button style="background: var(--gradient-success); color: white; border: none; padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 13px; font-weight: 600; cursor: pointer;">Analyze</button>
                                </div>
                            </div>
                            
                            <div style="padding: var(--space-6);">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="border-bottom: 1px solid var(--border-secondary);">
                                            <th style="padding: var(--space-4); text-align: left; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Product</th>
                                            <th style="padding: var(--space-4); text-align: center; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Sold</th>
                                            <th style="padding: var(--space-4); text-align: center; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Revenue</th>
                                            <th style="padding: var(--space-4); text-align: center; font-size: 13px; font-weight: 800; color: var(--text-secondary); text-transform: uppercase;">Trend</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="border-bottom: 1px solid var(--border-secondary); transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                            <td style="padding: var(--space-4); font-weight: 600; color: var(--text-primary);">iPhone 15 Pro Max</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700;">158</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700; color: var(--success);">$18.2K</td>
                                            <td style="padding: var(--space-4); text-align: center;"><span style="background: rgba(0, 212, 170, 0.1); color: var(--success); padding: var(--space-1) var(--space-3); border-radius: var(--radius-xl); font-size: 12px; font-weight: 700;">Rising</span></td>
                                        </tr>
                                        <tr style="border-bottom: 1px solid var(--border-secondary); transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                            <td style="padding: var(--space-4); font-weight: 600; color: var(--text-primary);">AirPods Pro Gen 3</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700;">165</td>
                                            <td style="padding: var(--space-4); text-align: center; font-family: 'Inter', sans-serif; font-weight: 700; color: var(--success);">$8.9K</td>
                                            <td style="padding: var(--space-4); text-align: center;"><span style="background: rgba(102, 126, 234, 0.1); color: var(--primary); padding: var(--space-1) var(--space-3); border-radius: var(--radius-xl); font-size: 12px; font-weight: 700;">Stable</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Futuristic Predictive Analytics -->
                    <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.1) 50%, rgba(255, 107, 157, 0.05) 100%); border: 1px solid rgba(102, 126, 234, 0.2); border-radius: var(--radius-3xl); padding: var(--space-12); position: relative; overflow: hidden;">
                        <!-- Animated Background -->
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(102,126,234,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>'); opacity: 0.3;"></div>
                        
                        <div style="position: relative; z-index: 2;">
                            <div style="text-align: center; margin-bottom: var(--space-12);">
                                <div style="width: 100px; height: 100px; background: var(--gradient-primary); border-radius: var(--radius-3xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 48px; margin: 0 auto var(--space-6); box-shadow: var(--shadow-2xl); animation: float 4s ease-in-out infinite;">🔮</div>
                                <h2 style="font-size: 42px; font-weight: 900; color: var(--primary); margin-bottom: var(--space-4);">Predictive Analytics Engine</h2>
                                <p style="font-size: 18px; color: var(--text-secondary); max-width: 700px; margin: 0 auto;">AI-powered forecasting and demand prediction models with machine learning algorithms</p>
                            </div>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--space-8);">
                                <!-- Stock Out Prediction -->
                                <div style="background: var(--bg-glass-strong); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); border-radius: var(--radius-2xl); padding: var(--space-8); text-align: center; position: relative; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'; this.style.boxShadow='0 25px 50px -12px rgba(0, 212, 170, 0.3)'" onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow=''">
                                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-success); border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;"></div>
                                    <div style="font-size: 48px; font-weight: 900; color: var(--success); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-success); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">7 days</div>
                                    <div style="font-size: 16px; color: var(--text-primary); font-weight: 700; margin-bottom: var(--space-2);">Stock Out Prediction</div>
                                    <div style="font-size: 14px; color: var(--text-tertiary);">Next critical shortage forecast</div>
                                </div>

                                <!-- Demand Forecast -->
                                <div style="background: var(--bg-glass-strong); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); border-radius: var(--radius-2xl); padding: var(--space-8); text-align: center; position: relative; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'; this.style.boxShadow='0 25px 50px -12px rgba(102, 126, 234, 0.3)'" onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow=''">
                                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-primary); border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;"></div>
                                    <div style="font-size: 48px; font-weight: 900; color: var(--primary); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">+23%</div>
                                    <div style="font-size: 16px; color: var(--text-primary); font-weight: 700; margin-bottom: var(--space-2);">Demand Forecast</div>
                                    <div style="font-size: 14px; color: var(--text-tertiary);">Projected growth next month</div>
                                </div>

                                <!-- Potential Savings -->
                                <div style="background: var(--bg-glass-strong); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); border-radius: var(--radius-2xl); padding: var(--space-8); text-align: center; position: relative; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'; this.style.boxShadow='0 25px 50px -12px rgba(255, 184, 0, 0.3)'" onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow=''">
                                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-warning); border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;"></div>
                                    <div style="font-size: 48px; font-weight: 900; color: var(--warning); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-warning); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">$12.4K</div>
                                    <div style="font-size: 16px; color: var(--text-primary); font-weight: 700; margin-bottom: var(--space-2);">Potential Savings</div>
                                    <div style="font-size: 14px; color: var(--text-tertiary);">AI optimization opportunities</div>
                                </div>

                                <!-- Model Accuracy -->
                                <div style="background: var(--bg-glass-strong); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); border-radius: var(--radius-2xl); padding: var(--space-8); text-align: center; position: relative; transition: all 0.4s var(--ease-spring);" onmouseover="this.style.transform='translateY(-8px) scale(1.05)'; this.style.boxShadow='0 25px 50px -12px rgba(255, 107, 157, 0.3)'" onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow=''">
                                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: var(--gradient-accent); border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;"></div>
                                    <div style="font-size: 48px; font-weight: 900; color: var(--accent); font-family: 'Inter', sans-serif; margin-bottom: var(--space-3); background: var(--gradient-accent); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">95.8%</div>
                                    <div style="font-size: 16px; color: var(--text-primary); font-weight: 700; margin-bottom: var(--space-2);">Model Accuracy</div>
                                    <div style="font-size: 14px; color: var(--text-tertiary);">Machine learning confidence</div>
                                </div>
                            </div>

                            <!-- CTA Button -->
                            <div style="text-align: center; margin-top: var(--space-12);">
                                <button style="background: var(--gradient-primary); color: white; border: none; padding: var(--space-4) var(--space-8); border-radius: var(--radius-2xl); font-size: 16px; font-weight: 700; cursor: pointer; transition: all 0.3s var(--ease-spring); box-shadow: var(--shadow-xl); display: inline-flex; align-items: center; gap: var(--space-3);" onmouseover="this.style.transform='translateY(-4px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <span>Generate Full Forecast Report</span>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="9,18 15,12 9,6"></polyline>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Live Activity Content -->
                <div id="activityContent" class="content page-section">
                    <div class="table-card">
                        <div class="table-header">
                            <h3 class="table-title">Live Activity Feed</h3>
                            <div class="table-actions">
                                <input type="search" class="search-input" placeholder="Search activities...">
                                <button class="btn">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="7,10 12,15 17,10"></polyline>
                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                    </svg>
                                    Export Activity Log
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="activity-list">
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: var(--gradient-success);">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M7 17l6-6 6 6"></path>
                                            <path d="M13 11v10"></path>
                                        </svg>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">Stock In: iPhone 15 Pro Max</div>
                                        <div class="activity-meta">+45 units • SKU: IP15PM001 • 2 minutes ago</div>
                                    </div>
                                </div>
                                
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: var(--gradient-warning);">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M17 7l-6 6-6-6"></path>
                                            <path d="M11 13V3"></path>
                                        </svg>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">Stock Out: MacBook Pro M3</div>
                                        <div class="activity-meta">-12 units • SKU: MBP24M3002 • 15 minutes ago</div>
                                    </div>
                                </div>
                                
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: var(--gradient-error);">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">Critical Alert: Magic Keyboard</div>
                                        <div class="activity-meta">Only 3 units remaining • SKU: MKB24004 • 1 hour ago</div>
                                    </div>
                                </div>
                                
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: var(--gradient-primary);">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                        </svg>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">New Product Added: Apple Vision Pro</div>
                                        <div class="activity-meta">50 initial stock • SKU: AVP24007 • 3 hours ago</div>
                                    </div>
                                </div>
                                
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: var(--gradient-accent);">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="22" y1="2" x2="11" y2="13"></line>
                                            <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                        </svg>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">WhatsApp Report Sent</div>
                                        <div class="activity-meta">Daily summary to admin group • 6 hours ago</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

               <!-- WhatsApp Reports Content -->
                <div id="whatsappContent" class="content page-section">
                    <!-- Modern Hero Section -->
                    <div style="background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); border-radius: var(--radius-3xl); padding: var(--space-12); margin-bottom: var(--space-12); color: white; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: -30px; right: -30px; width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>
                        <div style="position: relative; z-index: 2;">
                            <div style="display: flex; align-items: center; gap: var(--space-6); margin-bottom: var(--space-8);">
                                <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: var(--radius-3xl); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(12px);">
                                    <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516"/>
                                    </svg>
                                </div>
                                <div>
                                    <h2 style="font-size: 36px; font-weight: 900; margin-bottom: var(--space-2);">WhatsApp Integration</h2>
                                    <p style="font-size: 18px; opacity: 0.9;">Automated reports and alerts delivered directly to your WhatsApp</p>
                                </div>
                            </div>
                            
                            <!-- Quick Stats -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: var(--space-6);">
                                <div style="background: rgba(255,255,255,0.15); padding: var(--space-6); border-radius: var(--radius-2xl); backdrop-filter: blur(12px); text-align: center;">
                                    <div style="font-size: 32px; font-weight: 900; margin-bottom: var(--space-2);">24/7</div>
                                    <div style="font-size: 14px; opacity: 0.9;">Auto Monitoring</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); padding: var(--space-6); border-radius: var(--radius-2xl); backdrop-filter: blur(12px); text-align: center;">
                                    <div style="font-size: 32px; font-weight: 900; margin-bottom: var(--space-2);">Instant</div>
                                    <div style="font-size: 14px; opacity: 0.9;">Delivery</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); padding: var(--space-6); border-radius: var(--radius-2xl); backdrop-filter: blur(12px); text-align: center;">
                                    <div style="font-size: 32px; font-weight: 900; margin-bottom: var(--space-2);">Multi</div>
                                    <div style="font-size: 14px; opacity: 0.9;">Recipients</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- WhatsApp Configuration Card -->
                    <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); padding: var(--space-10); margin-bottom: var(--space-12); box-shadow: var(--shadow-2xl);">
                        <div style="margin-bottom: var(--space-10);">
                            <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-4);">
                                <div style="width: 48px; height: 48px; background: var(--gradient-success); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white;">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="3"></circle>
                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 style="font-size: 24px; font-weight: 800; color: var(--text-primary); margin-bottom: var(--space-1);">API Configuration</h3>
                                    <p style="color: var(--text-secondary);">Configure your Fonnte WhatsApp API settings</p>
                                </div>
                            </div>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-8); margin-bottom: var(--space-10);">
                            <div class="form-group">
                                <label class="form-label">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                        <circle cx="12" cy="16" r="1"></circle>
                                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                    </svg>
                                    Fonnte API Token
                                </label>
                                <input type="password" class="form-input" id="waApiToken" placeholder="Masukkan token Fonnte API Anda" autocomplete="off">
                                <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2); display: flex; align-items: center; gap: var(--space-2);">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M9,9h6v6H9z"></path>
                                    </svg>
                                    Dapatkan token dari <a href="https://fonnte.com" target="_blank" style="color: var(--primary); font-weight: 600;">fonnte.com</a>
                                </small>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                    </svg>
                                    Nomor Admin 1
                                </label>
                                <input type="text" class="form-input" id="waAdmin1Phone" placeholder="628123456789" pattern="[0-9]*">
                                <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2);">Format: 628xxxxxxxxx (tanpa tanda +)</small>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                    </svg>
                                    Nomor Admin 2
                                </label>
                                <input type="text" class="form-input" id="waAdmin2Phone" placeholder="628123456790" pattern="[0-9]*">
                                <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2);">Format: 628xxxxxxxxx (opsional)</small>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                    ID Grup WhatsApp
                                </label>
                                <input type="text" class="form-input" id="waGroupId" placeholder="<EMAIL>">
                                <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2);">ID grup WhatsApp untuk laporan tim</small>
                            </div>
                        </div>
                        
                        <!-- Auto Report Settings -->
                        <div style="background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(37, 211, 102, 0.05) 100%); border: 1px solid rgba(37, 211, 102, 0.2); border-radius: var(--radius-2xl); padding: var(--space-8); margin-bottom: var(--space-8);">
                            <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-6);">
                                <div style="width: 40px; height: 40px; background: var(--gradient-success); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white;">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <polyline points="12,6 12,12 16,14"></polyline>
                                    </svg>
                                </div>
                                <div>
                                    <h4 style="font-size: 18px; font-weight: 700; color: var(--text-primary); margin-bottom: var(--space-1);">Laporan Otomatis</h4>
                                    <p style="color: var(--text-secondary); font-size: 14px; margin: 0;">Konfigurasi pengiriman laporan harian otomatis</p>
                                </div>
                            </div>
                            
                            <div style="display: grid; grid-template-columns: 1fr auto; gap: var(--space-8); align-items: center;">
                                <div style="display: grid; grid-template-columns: 1fr 200px; gap: var(--space-6); align-items: end;">
                                    <div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-4);">
                                            <div>
                                                <div style="font-weight: 700; color: var(--text-primary); margin-bottom: var(--space-1);">Auto-Report Harian</div>
                                                <div style="font-size: 14px; color: var(--text-tertiary);">Kirim laporan stok secara otomatis setiap hari</div>
                                            </div>
                                            <label class="settings-toggle">
                                                <input type="checkbox" id="waAutoReportToggle" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group" style="margin: 0;">
                                        <label class="form-label" style="margin-bottom: var(--space-2);">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 6px;">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <polyline points="12,6 12,12 16,14"></polyline>
                                            </svg>
                                            Jam Kirim
                                        </label>
                                        <input type="time" class="form-input" id="waAutoReportTime" value="18:00" style="font-family: var(--font-mono);">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div style="display: flex; gap: var(--space-4); flex-wrap: wrap;">
                            <button class="btn btn-primary" id="saveWhatsAppConfig" style="flex: 1; min-width: 200px;">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                                </svg>
                                Simpan Konfigurasi
                            </button>
                            
                            <button class="btn btn-accent" id="sendTestReport" style="flex: 1; min-width: 200px;">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                    <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                </svg>
                                Tes Kirim Sekarang
                            </button>
                        </div>
                    </div>

                    <!-- Report History with Enhanced Design -->
                    <div style="background: var(--bg-glass-strong); backdrop-filter: blur(40px); border: 1px solid var(--border-primary); border-radius: var(--radius-3xl); overflow: hidden; box-shadow: var(--shadow-2xl);">
                        <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%); padding: var(--space-8); border-bottom: 1px solid var(--border-secondary);">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center; gap: var(--space-4);">
                                    <div style="width: 48px; height: 48px; background: var(--gradient-primary); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; color: white;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                            <polyline points="14,2 14,8 20,8"></polyline>
                                            <line x1="16" y1="13" x2="8" y2="13"></line>
                                            <line x1="16" y1="17" x2="8" y2="17"></line>
                                            <polyline points="10,9 9,9 8,9"></polyline>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 style="font-size: 22px; font-weight: 800; color: var(--text-primary); margin-bottom: var(--space-1);">Riwayat Pengiriman</h3>
                                        <p style="color: var(--text-secondary); margin: 0;">Log semua laporan WhatsApp yang telah dikirim</p>
                                    </div>
                                </div>
                                <div style="background: rgba(37, 211, 102, 0.1); color: var(--success); padding: var(--space-2) var(--space-4); border-radius: var(--radius-xl); font-size: 13px; font-weight: 700;">
                                    <span id="totalReportsSent">3 Laporan Terkirim</span>
                                </div>
                            </div>
                        </div>
                        
                        <div style="overflow-x: auto;">
                            <table class="table" style="margin: 0;">
                                <thead>
                                    <tr>
                                        <th style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                                    <line x1="16" y1="2" x2="16" y2="6"></line>
                                                    <line x1="8" y1="2" x2="8" y2="6"></line>
                                                    <line x1="3" y1="10" x2="21" y2="10"></line>
                                                </svg>
                                                Tanggal & Waktu
                                            </div>
                                        </th>
                                        <th style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                    <polyline points="14,2 14,8 20,8"></polyline>
                                                </svg>
                                                Jenis Laporan
                                            </div>
                                        </th>
                                        <th style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                                    <circle cx="9" cy="7" r="4"></circle>
                                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                                </svg>
                                                Penerima
                                            </div>
                                        </th>
                                        <th style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                                </svg>
                                                Status
                                            </div>
                                        </th>
                                        <th style="padding: var(--space-6);">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="reportHistoryBody">
                                    <tr style="transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                        <td style="padding: var(--space-6);">
                                            <div style="font-family: var(--font-mono); font-weight: 600; color: var(--text-primary);">Jun 10, 2025</div>
                                            <div style="font-size: 13px; color: var(--text-tertiary); font-family: var(--font-mono);">08:00 WIB</div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-3);">
                                                <div style="width: 32px; height: 32px; background: var(--gradient-success); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white;">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                        <polyline points="14,2 14,8 20,8"></polyline>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div style="font-weight: 600; color: var(--text-primary);">Laporan Harian</div>
                                                    <div style="font-size: 13px; color: var(--text-tertiary);">Stock Summary</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <div style="background: rgba(37, 211, 102, 0.1); color: var(--success); padding: var(--space-2) var(--space-3); border-radius: var(--radius-lg); font-size: 13px; font-weight: 600; text-align: center;">
                                                Grup + 2 Admin
                                            </div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <span class="status-badge safe">
                                                <span class="status-dot"></span>Terkirim
                                            </span>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                                    <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                                </svg>
                                                Kirim Ulang
                                            </button>
                                        </td>
                                    </tr>
                                    
                                    <tr style="transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                        <td style="padding: var(--space-6);">
                                            <div style="font-family: var(--font-mono); font-weight: 600; color: var(--text-primary);">Jun 9, 2025</div>
                                            <div style="font-size: 13px; color: var(--text-tertiary); font-family: var(--font-mono);">18:30 WIB</div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-3);">
                                                <div style="width: 32px; height: 32px; background: var(--gradient-error); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white;">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div style="font-weight: 600; color: var(--text-primary);">Stok Kritis</div>
                                                    <div style="font-size: 13px; color: var(--text-tertiary);">Critical Alert</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <div style="background: rgba(255, 87, 87, 0.1); color: var(--error); padding: var(--space-2) var(--space-3); border-radius: var(--radius-lg); font-size: 13px; font-weight: 600; text-align: center;">
                                                Grup + 2 Admin
                                            </div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <span class="status-badge safe">
                                                <span class="status-dot"></span>Terkirim
                                            </span>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                                    <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                                </svg>
                                                Kirim Ulang
                                            </button>
                                        </td>
                                    </tr>
                                    
                                    <tr style="transition: all 0.3s var(--ease-spring);" onmouseover="this.style.background='var(--bg-glass)'" onmouseout="this.style.background=''">
                                        <td style="padding: var(--space-6);">
                                            <div style="font-family: var(--font-mono); font-weight: 600; color: var(--text-primary);">Jun 9, 2025</div>
                                            <div style="font-size: 13px; color: var(--text-tertiary); font-family: var(--font-mono);">08:00 WIB</div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <div style="display: flex; align-items: center; gap: var(--space-3);">
                                                <div style="width: 32px; height: 32px; background: var(--gradient-success); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white;">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                        <polyline points="14,2 14,8 20,8"></polyline>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div style="font-weight: 600; color: var(--text-primary);">Laporan Harian</div>
                                                    <div style="font-size: 13px; color: var(--text-tertiary);">Stock Summary</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <div style="background: rgba(37, 211, 102, 0.1); color: var(--success); padding: var(--space-2) var(--space-3); border-radius: var(--radius-lg); font-size: 13px; font-weight: 600; text-align: center;">
                                                Grup + 2 Admin
                                            </div>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <span class="status-badge safe">
                                                <span class="status-dot"></span>Terkirim
                                            </span>
                                        </td>
                                        <td style="padding: var(--space-6);">
                                            <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                                    <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                                </svg>
                                                Kirim Ulang
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Settings Content -->
                <div id="settingsContent" class="content page-section">
                    <div style="display: grid; gap: var(--space-12);">
                        <!-- WhatsApp Configuration -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">WhatsApp Configuration</h3>
                                <p class="card-subtitle">Configure automatic WhatsApp reporting settings</p>
                            </div>
                            <div class="card-content">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">Fonnte API Key</label>
                                        <input type="password" class="form-input" id="settingsFontteKey" placeholder="Enter your Fonnte API key">
                                        <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2);">Get your API key from <a href="https://fonnte.com" target="_blank" style="color: var(--primary);">fonnte.com</a></small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Admin Phone 1</label>
                                        <input type="text" class="form-input" id="settingsAdmin1" placeholder="628123456789">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Admin Phone 2</label>
                                        <input type="text" class="form-input" id="settingsAdmin2" placeholder="628123456790">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">WhatsApp Group ID</label>
                                        <input type="text" class="form-input" id="settingsGroupId" placeholder="<EMAIL>">
                                        <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2);">WhatsApp group ID for team reports</small>
                                    </div>
                                </div>
                                
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-6); margin-top: var(--space-8);">
                                    <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space-6); background: var(--bg-glass); border-radius: var(--radius-xl); border: 1px solid var(--border-primary);">
                                        <div>
                                            <div style="font-weight: 700; color: var(--text-primary); margin-bottom: var(--space-2);">Auto-Report Harian</div>
                                            <div style="font-size: 14px; color: var(--text-tertiary);">Kirim laporan otomatis setiap hari</div>
                                        </div>
                                        <label class="settings-toggle">
                                            <input type="checkbox" id="autoReportToggle" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Jam Kirim Otomatis</label>
                                        <input type="time" class="form-input" id="autoReportTime" value="18:00">
                                        <small style="color: var(--text-tertiary); font-size: 13px; margin-top: var(--space-2);">Waktu pengiriman laporan harian (format 24 jam)</small>
                                    </div>
                                </div>
                                
                                <div style="display: flex; gap: var(--space-4); margin-top: var(--space-8);">
                                    <button class="btn btn-primary" id="saveSettings">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        Save Settings
                                    </button>
                                    <button class="btn btn-accent" id="testWhatsAppNow">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="22" y1="2" x2="11" y2="13"></line>
                                            <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                        </svg>
                                        Tes Kirim Sekarang
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Appearance & Theme</h3>
                                <p class="card-subtitle">Customize the look and feel of your StockFlow Pro</p>
                            </div>
                            <div class="card-content">
                                <div style="display: flex; align-items: center; gap: var(--space-8);">
                                    <label style="color: var(--text-secondary); font-weight: 700; flex: 1;">Dark mode preference</label>
                                    <button class="theme-toggle" id="settingsThemeToggle" style="position: relative;">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Notification Preferences</h3>
                                <p class="card-subtitle">Configure your notification and alert preferences</p>
                            </div>
                            <div class="card-content">
                                <div style="display: grid; gap: var(--space-6);">
                                    <label style="display: flex; align-items: center; gap: var(--space-4); color: var(--text-secondary); font-weight: 600; cursor: pointer;">
                                        <input type="checkbox" checked style="margin: 0; transform: scale(1.2);"> 
                                        Low stock alerts via WhatsApp
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--space-4); color: var(--text-secondary); font-weight: 600; cursor: pointer;">
                                        <input type="checkbox" checked style="margin: 0; transform: scale(1.2);"> 
                                        Daily inventory reports
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--space-4); color: var(--text-secondary); font-weight: 600; cursor: pointer;">
                                        <input type="checkbox" style="margin: 0; transform: scale(1.2);"> 
                                        Email notifications
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--space-4); color: var(--text-secondary); font-weight: 600; cursor: pointer;">
                                        <input type="checkbox" checked style="margin: 0; transform: scale(1.2);"> 
                                        Real-time activity feed
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Data Management</h3>
                                <p class="card-subtitle">Import, export, and backup your inventory data</p>
                            </div>
                            <div class="card-content">
                                <div style="display: flex; gap: var(--space-5); flex-wrap: wrap;">
                                    <button class="btn">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7,10 12,15 17,10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg>
                                        Export All Data
                                    </button>
                                    <button class="btn">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 15v4a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-4"></path>
                                            <polyline points="17,8 12,3 7,8"></polyline>
                                            <line x1="12" y1="3" x2="12" y2="15"></line>
                                        </svg>
                                        Import Data
                                    </button>
                                    <button class="btn btn-accent">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="3"></circle>
                                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                        </svg>
                                        Create Backup
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">System Information</h3>
                                <p class="card-subtitle">StockFlow Pro version and system details</p>
                            </div>
                            <div class="card-content">
                                <div style="display: grid; gap: var(--space-4); font-family: var(--font-mono); font-size: 14px;">
                                    <div style="display: flex; justify-content: space-between;">
                                        <span style="color: var(--text-tertiary);">Version:</span>
                                        <span style="color: var(--text-primary); font-weight: 600;">StockFlow Pro v2.4.1</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between;">
                                        <span style="color: var(--text-tertiary);">Last Updated:</span>
                                        <span style="color: var(--text-primary); font-weight: 600;">June 10, 2025</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between;">
                                        <span style="color: var(--text-tertiary);">Database:</span>
                                        <span style="color: var(--success); font-weight: 600;">Connected</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between;">
                                        <span style="color: var(--text-tertiary);">WhatsApp API:</span>
                                        <span style="color: var(--success); font-weight: 600;">Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Firebase Configuration - FIXED VERSION
        const firebaseConfig = {
            apiKey: "AIzaSyDzQeSBaemkuNwIulwxIzJL5YSVYdsogqU",
            authDomain: "stockflow-inventory.firebaseapp.com",
            projectId: "stockflow-inventory",
            storageBucket: "stockflow-inventory.firebasestorage.app",
            messagingSenderId: "131390386040",
            appId: "1:131390386040:web:3f722b986bc668159da28d"
        };
        
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();
        const auth = firebase.auth();

        // Firebase Service Class - FIXED WITH CORRECT CONFIG
        class FirebaseService {
            constructor() {
                this.user = null;
                this.initAuth();
            }

            initAuth() {
                // FIXED: Check if auth exists before using it
                if (typeof auth !== 'undefined' && auth) {
                    auth.onAuthStateChanged((user) => {
                        this.user = user;
                        if (user) {
                            console.log('User logged in:', user.email);
                            this.loadUserData();
                        } else {
                            console.log('User logged out');
                        }
                    });
                } else {
                    // Wait for auth to be available
                    const waitForAuth = setInterval(() => {
                        if (typeof auth !== 'undefined' && auth) {
                            clearInterval(waitForAuth);
                            auth.onAuthStateChanged((user) => {
                                this.user = user;
                                if (user) {
                                    console.log('User logged in:', user.email);
                                    this.loadUserData();
                                } else {
                                    console.log('User logged out');
                                }
                            });
                        }
                    }, 100);
                }
            }

            async register(email, password, fullName) {
                try {
                    // FIXED: Check if auth exists
                    if (typeof auth === 'undefined' || !auth) {
                        throw new Error('Firebase auth not initialized');
                    }
                    
                    const result = await auth.createUserWithEmailAndPassword(email, password);
                    const user = result.user;
                    
                    // Parse fullName into first and last name
                    const nameParts = fullName.trim().split(' ');
                    const firstName = nameParts[0] || '';
                    const lastName = nameParts.slice(1).join(' ') || '';
                    
                    // Save additional user data to Firestore
                    await db.collection('users').doc(user.uid).set({
                        firstName: firstName,
                        lastName: lastName,
                        fullName: fullName,
                        email: email,
                        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                        role: 'admin'
                    });

                    // Update profile
                    await user.updateProfile({
                        displayName: fullName
                    });

                    return { success: true, user: user };
                } catch (error) {
                    console.error('Registration error:', error);
                    return { success: false, error: error.message };
                }
            }

            async login(email, password) {
                try {
                    // FIXED: Check if auth exists
                    if (typeof auth === 'undefined' || !auth) {
                        throw new Error('Firebase auth not initialized');
                    }
                    
                    const result = await auth.signInWithEmailAndPassword(email, password);
                    return { success: true, user: result.user };
                } catch (error) {
                    console.error('Login error:', error);
                    return { success: false, error: error.message };
                }
            }

            async logout() {
                try {
                    // FIXED: Check if auth exists
                    if (typeof auth === 'undefined' || !auth) {
                        throw new Error('Firebase auth not initialized');
                    }
                    
                    await auth.signOut();
                    return { success: true };
                } catch (error) {
                    console.error('Logout error:', error);
                    return { success: false, error: error.message };
                }
            }

            async loadUserData() {
                if (!this.user) return null;
                
                try {
                    // FIXED: Check if db exists
                    if (typeof db === 'undefined' || !db) {
                        console.log('Firebase firestore not initialized');
                        return null;
                    }
                    
                    const doc = await db.collection('users').doc(this.user.uid).get();
                    if (doc.exists) {
                        return doc.data();
                    }
                    return null;
                } catch (error) {
                    console.error('Error loading user data:', error);
                    return null;
                }
            }

            async saveProduct(product) {
                if (!this.user) throw new Error('User not authenticated');
                
                try {
                    // FIXED: Check if db exists
                    if (typeof db === 'undefined' || !db) {
                        throw new Error('Firebase firestore not initialized');
                    }
                    
                    const docRef = await db.collection('products').add({
                        ...product,
                        userId: this.user.uid,
                        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                        stockIn: 0,
                        stockOut: 0,
                        currentStock: product.initialStock
                    });
                    
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error('Error saving product:', error);
                    return { success: false, error: error.message };
                }
            }

            async loadProducts() {
                if (!this.user) return [];
                
                try {
                    // FIXED: Check if db exists
                    if (typeof db === 'undefined' || !db) {
                        console.log('Firebase firestore not initialized');
                        return [];
                    }
                    
                    const snapshot = await db.collection('products')
                        .where('userId', '==', this.user.uid)
                        .get();
                    
                    const products = [];
                    snapshot.forEach(doc => {
                        products.push({ id: doc.id, ...doc.data() });
                    });
                    
                    return products;
                } catch (error) {
                    console.error('Error loading products:', error);
                    return [];
                }
            }

            async saveTransaction(transaction) {
                if (!this.user) throw new Error('User not authenticated');
                
                try {
                    // FIXED: Check if db exists
                    if (typeof db === 'undefined' || !db) {
                        throw new Error('Firebase firestore not initialized');
                    }
                    
                    await db.collection('transactions').add({
                        ...transaction,
                        userId: this.user.uid,
                        timestamp: firebase.firestore.FieldValue.serverTimestamp()
                    });
                    
                    return { success: true };
                } catch (error) {
                    console.error('Error saving transaction:', error);
                    return { success: false, error: error.message };
                }
            }
        }

        // Initialize Firebase service
        let firebaseService;

        // Advanced Fonnte WhatsApp Integration Service
        class WhatsAppService {
            constructor() {
                this.config = this.loadConfig();
                this.schedules = this.loadSchedules();
                this.initScheduler();
            }

            loadConfig() {
                return JSON.parse(localStorage.getItem('whatsappConfig') || '{}');
            }

            saveConfig(config) {
                localStorage.setItem('whatsappConfig', JSON.stringify(config));
                this.config = config;
            }

            loadSchedules() {
                const defaultSchedules = {
                    dailyReport: { active: true, time: '18:00' },
                    criticalAlert: { active: true, interval: 6 },
                    weeklyReport: { active: false, day: 1, time: '09:00' },
                    monthlyReport: { active: false, date: 1, time: '10:00' }
                };
                return JSON.parse(localStorage.getItem('reportSchedules') || JSON.stringify(defaultSchedules));
            }

            saveSchedules() {
                localStorage.setItem('reportSchedules', JSON.stringify(this.schedules));
            }

            async sendMessage(targets, message) {
                if (!this.config.token) {
                    throw new Error('Fonnte token not configured');
                }

                const targetString = Array.isArray(targets) ? targets.join(',') : targets;
                
                try {
                    const formData = new FormData();
                    formData.append('target', targetString);
                    formData.append('message', message);
                    formData.append('delay', '2');

                    const response = await fetch('https://api.fonnte.com/send', {
                        method: 'POST',
                        headers: {
                            'Authorization': this.config.token
                        },
                        body: formData
                    });

                    const result = await response.json();
                    this.logReport('Auto Report', targetString, result.status ? 'Delivered' : 'Failed');
                    return result;
                } catch (error) {
                    console.error('WhatsApp send error:', error);
                    this.logReport('Auto Report', targetString, 'Failed');
                    throw error;
                }
            }

            generateDailyReport() {
                const stockInToday = document.getElementById('stockIn').textContent;
                const stockOutToday = document.getElementById('stockOut').textContent;
                const criticalStock = document.getElementById('criticalStock').textContent;
                const totalProducts = document.getElementById('totalProducts').textContent;

                return `🏢 *StockFlow Pro - Daily Report*
📅 ${new Date().toLocaleDateString('id-ID', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
})}

📊 *Inventory Summary*
• Total Products: ${totalProducts}
• Stock In Today: +${stockInToday}
• Stock Out Today: -${stockOutToday}
• Critical Stock Items: ${criticalStock}

${parseInt(criticalStock) > 0 ? '⚠️ *Action Required*\nSome items need immediate restocking!' : '✅ All inventory levels are healthy'}

Generated by StockFlow Pro 🚀`;
            }

            generateCriticalAlert() {
                const criticalItems = this.getCriticalStockItems();
                if (criticalItems.length === 0) return null;

                return `🚨 *CRITICAL STOCK ALERT*
⚠️ Immediate attention required!

📦 *Critical Items (≤5 units)*:
${criticalItems.map(item => `• ${item.name} (${item.sku}): ${item.stock} units`).join('\n')}

🔄 *Recommended Actions*:
• Reorder immediately
• Check supplier availability
• Update procurement team

Generated by StockFlow Pro 🚀`;
            }

            getCriticalStockItems() {
                const items = [];
                const rows = document.querySelectorAll('#inventoryTableBody tr');
                
                rows.forEach(row => {
                    const cells = row.cells;
                    const stock = parseInt(cells[6].textContent);
                    if (stock <= 5) {
                        items.push({
                            name: cells[0].textContent,
                            sku: cells[1].textContent,
                            stock: stock
                        });
                    }
                });
                
                return items;
            }

            getTargets() {
                const targets = [];
                if (this.config.admin1Phone) targets.push(this.config.admin1Phone);
                if (this.config.admin2Phone) targets.push(this.config.admin2Phone);
                if (this.config.groupId) targets.push(this.config.groupId);
                return targets;
            }

            async sendDailyReport() {
                if (!this.schedules.dailyReport.active) return;
                
                const message = this.generateDailyReport();
                const targets = this.getTargets();
                
                if (targets.length > 0) {
                    try {
                        await this.sendMessage(targets, message);
                        console.log('Daily report sent successfully');
                    } catch (error) {
                        console.error('Failed to send daily report:', error);
                    }
                }
            }

            async sendCriticalAlert() {
                if (!this.schedules.criticalAlert.active) return;
                
                const message = this.generateCriticalAlert();
                if (!message) return; // No critical items
                
                const targets = this.getTargets();
                
                if (targets.length > 0) {
                    try {
                        await this.sendMessage(targets, message);
                        console.log('Critical alert sent successfully');
                    } catch (error) {
                        console.error('Failed to send critical alert:', error);
                    }
                }
            }

            async sendTestReport() {
                const message = `🧪 *StockFlow Pro - Test Report*
📅 ${new Date().toLocaleString('id-ID')}

✅ WhatsApp integration is working perfectly!

📊 Current Status:
• System: Online
• Database: Connected
• API: Active

This is a test message from your StockFlow Pro system 🚀`;

                const targets = this.getTargets();
                
                if (targets.length === 0) {
                    alert('Please configure WhatsApp settings first!');
                    return false;
                }

                try {
                    const result = await this.sendMessage(targets, message);
                    if (result.status) {
                        alert('✅ Test report sent successfully!');
                        return true;
                    } else {
                        alert('❌ Failed to send test report. Please check your configuration.');
                        return false;
                    }
                } catch (error) {
                    alert('❌ Error sending test report: ' + error.message);
                    return false;
                }
            }

            logReport(type, targets, status) {
                const reports = JSON.parse(localStorage.getItem('reportHistory') || '[]');
                const report = {
                    id: Date.now(),
                    timestamp: new Date().toISOString(),
                    type: type,
                    targets: targets,
                    status: status
                };
                
                reports.unshift(report);
                if (reports.length > 100) reports.pop();
                
                localStorage.setItem('reportHistory', JSON.stringify(reports));
                this.updateReportHistoryTable();
            }

            updateReportHistoryTable() {
                const tbody = document.getElementById('reportHistoryBody');
                if (!tbody) return;

                const reports = JSON.parse(localStorage.getItem('reportHistory') || '[]');
                
                reports.slice(0, 10).forEach((report, index) => {
                    if (index < 3) return;
                    
                    const row = document.createElement('tr');
                    const date = new Date(report.timestamp);
                    const statusBadge = report.status === 'Delivered' ? 'safe' : 'danger';
                    
                    row.innerHTML = `
                        <td>${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })} ${date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}</td>
                        <td>${report.type}</td>
                        <td>${this.formatTargets(report.targets)}</td>
                        <td><span class="status-badge ${statusBadge}"><span class="status-dot"></span>${report.status}</span></td>
                        <td><button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;" onclick="whatsAppService.resendReport('${report.id}')">Resend</button></td>
                    `;
                    
                    tbody.appendChild(row);
                });
            }

            formatTargets(targets) {
                const targetArray = targets.split(',');
                let description = '';
                
                if (targetArray.some(t => t.includes('@g.us'))) {
                    description += 'Admin Group';
                }
                
                const phoneCount = targetArray.filter(t => !t.includes('@g.us')).length;
                if (phoneCount > 0) {
                    description += (description ? ' + ' : '') + `${phoneCount} Admin${phoneCount > 1 ? 's' : ''}`;
                }
                
                return description || 'Unknown';
            }

            async resendReport(reportId) {
                const reports = JSON.parse(localStorage.getItem('reportHistory') || '[]');
                const report = reports.find(r => r.id == reportId);
                
                if (!report) return;
                
                try {
                    const message = report.type.includes('Daily') ? this.generateDailyReport() : this.generateCriticalAlert();
                    if (message) {
                        await this.sendMessage(report.targets, message);
                        alert('✅ Report resent successfully!');
                    }
                } catch (error) {
                    alert('❌ Failed to resend report: ' + error.message);
                }
            }

            initScheduler() {
                setInterval(() => {
                    const now = new Date();
                    const timeStr = now.toTimeString().slice(0, 5);
                    
                    if (this.schedules.dailyReport.active && timeStr === this.schedules.dailyReport.time) {
                        this.sendDailyReport();
                    }
                }, 60000);

                setInterval(() => {
                    if (this.schedules.criticalAlert.active) {
                        const criticalItems = this.getCriticalStockItems();
                        if (criticalItems.length > 0) {
                            this.sendCriticalAlert();
                        }
                    }
                }, this.schedules.criticalAlert.interval * 60 * 60 * 1000);
            }
        }

        // Initialize WhatsApp service
        let whatsAppService;

        // Enhanced Theme Management
        function initTheme() {
            const themeToggle = document.getElementById('themeToggle');
            const settingsThemeToggle = document.getElementById('settingsThemeToggle');
            const body = document.body;

            const savedTheme = localStorage.getItem('theme') || 'light';
            body.setAttribute('data-theme', savedTheme);

            function toggleTheme() {
                const currentTheme = body.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                body.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcons(newTheme);
            }

            function updateThemeIcons(theme) {
                const lightIcon = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="5"></circle>
                    <line x1="12" y1="1" x2="12" y2="3"></line>
                    <line x1="12" y1="21" x2="12" y2="23"></line>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                    <line x1="1" y1="12" x2="3" y2="12"></line>
                    <line x1="21" y1="12" x2="23" y2="12"></line>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>`;
                
                const darkIcon = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>`;
                
                if (themeToggle) themeToggle.innerHTML = theme === 'dark' ? lightIcon : darkIcon;
                if (settingsThemeToggle) settingsThemeToggle.innerHTML = theme === 'dark' ? lightIcon : darkIcon;
            }

            updateThemeIcons(savedTheme);
            if (themeToggle) themeToggle.addEventListener('click', toggleTheme);
            if (settingsThemeToggle) settingsThemeToggle.addEventListener('click', toggleTheme);
        }

        // Enhanced Sidebar Management
        function initSidebar() {
            const menuButton = document.getElementById('menuButton');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            function toggleSidebar() {
                if (window.innerWidth <= 1200) {
                    sidebar.classList.toggle('open');
                    overlay.classList.toggle('open');
                }
            }

            function closeSidebar() {
                sidebar.classList.remove('open');
                overlay.classList.remove('open');
            }

            if (menuButton) menuButton.addEventListener('click', toggleSidebar);
            if (overlay) overlay.addEventListener('click', closeSidebar);

            window.addEventListener('resize', () => {
                if (window.innerWidth > 1200) {
                    closeSidebar();
                }
            });
        }

        // Enhanced Navigation Management
        function initNavigation() {
            const navLinks = document.querySelectorAll('.nav-link[data-page]');
            const pageTitle = document.getElementById('pageTitle');
            const contentSections = {
                'dashboard': document.getElementById('dashboardContent'),
                'inventory': document.getElementById('inventoryContent'),
                'stock-in': document.getElementById('stockInContent'),
                'stock-out': document.getElementById('stockOutContent'),
                'activity': document.getElementById('activityContent'),
                'reports': document.getElementById('reportsContent'),
                'whatsapp': document.getElementById('whatsappContent'),
                'settings': document.getElementById('settingsContent')
            };

            function showPage(pageName) {
                // Hide all sections
                Object.values(contentSections).forEach(section => {
                    if (section) section.classList.remove('active');
                });
                
                // Show selected section
                if (contentSections[pageName]) {
                    contentSections[pageName].classList.add('active');
                }
                
                // Update page title
                const titles = {
                    'dashboard': 'Dashboard',
                    'inventory': 'Inventory Management',
                    'stock-in': 'Stock In',
                    'stock-out': 'Stock Out',
                    'activity': 'Live Activity Feed',
                    'reports': 'Reports & Analytics',
                    'whatsapp': 'WhatsApp Reports',
                    'settings': 'Settings & Preferences'
                };
                if (pageTitle) pageTitle.textContent = titles[pageName] || 'Dashboard';
                
                // Update navigation active state
                navLinks.forEach(link => link.classList.remove('active'));
                const activeLink = document.querySelector(`[data-page="${pageName}"]`);
                if (activeLink) activeLink.classList.add('active');
                
                // Refresh data when navigating
                if (pageName === 'stock-in' || pageName === 'stock-out') {
                    setTimeout(populateProductDropdowns, 100);
                } else if (pageName === 'whatsapp') {
                    loadWhatsAppConfig();
                } else if (pageName === 'settings') {
                    loadSettingsConfig();
                } else if (pageName === 'reports') {
                    setTimeout(initReportsCharts, 200);
                } else if (pageName === 'dashboard') {
                    setTimeout(initCharts, 200);
                }
                
                // Close sidebar on mobile
                if (window.innerWidth <= 1200) {
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('overlay');
                    if (sidebar) sidebar.classList.remove('open');
                    if (overlay) overlay.classList.remove('open');
                }
            }

            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.getAttribute('data-page');
                    showPage(page);
                });
            });

            window.showPage = showPage;
        }

        // Global variables untuk chart instances
        let stockChartInstance;
        let topProductsChartInstance;

        // Enhanced Login Management
        function initLogin() {
            const loginPage = document.getElementById('loginPage');
            const registerPage = document.getElementById('registerPage');
            const mainApp = document.getElementById('mainApp');
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            const showRegisterBtn = document.getElementById('showRegisterBtn');
            const showLoginBtn = document.getElementById('showLoginBtn');

            // Show/Hide Pages
            function showLoginPage() {
                loginPage.classList.add('active');
                registerPage.classList.remove('active');
                mainApp.classList.remove('active');
            }

            function showRegisterPage() {
                registerPage.classList.add('active');
                loginPage.classList.remove('active');
                mainApp.classList.remove('active');
            }

            function showDashboard(user) {
                loginPage.classList.remove('active');
                registerPage.classList.remove('active');
                mainApp.classList.add('active');
                
                const userName = user.displayName || user.email.split('@')[0];
                const initials = userName.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
                
                const userAvatar = document.getElementById('userAvatar');
                const userNameEl = document.getElementById('userName');
                
                if (userAvatar) userAvatar.textContent = initials;
                if (userNameEl) userNameEl.textContent = userName;
                
                // Initialize dashboard
                setTimeout(() => {
                    const dashboardContent = document.getElementById('dashboardContent');
                    if (dashboardContent) dashboardContent.classList.add('active');
                    
                    const dashboardNav = document.querySelector('[data-page="dashboard"]');
                    if (dashboardNav) dashboardNav.classList.add('active');
                    
                    initDashboard();
                    whatsAppService = new WhatsAppService();
                    loadFirebaseData();
                }, 300);
            }

            async function logout() {
                const result = await firebaseService.logout();
                if (result.success) {
                    showLoginPage();
                } else {
                    alert('Error logging out: ' + result.error);
                }
            }

            // Login Form
            if (loginForm) {
                loginForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const email = e.target.querySelector('input[type="email"]').value;
                    const password = e.target.querySelector('input[type="password"]').value;
                    
                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.textContent = 'Signing in...';
                    submitBtn.disabled = true;
                    
                    const result = await firebaseService.login(email, password);
                    
                    if (result.success) {
                        showDashboard(result.user);
                    } else {
                        alert('❌ Login failed: ' + result.error);
                    }
                    
                    submitBtn.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10,17 15,12 10,7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        Sign in to StockFlow Pro
                    `;
                    submitBtn.disabled = false;
                });
            }

            // Register Form - SIMPLIFIED
            if (registerForm) {
                registerForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const fullName = e.target.querySelector('input[name="fullName"]').value;
                    const email = e.target.querySelector('input[name="email"]').value;
                    const password = e.target.querySelector('input[name="password"]').value;
                    const confirmPassword = e.target.querySelector('input[name="confirmPassword"]').value;
                    
                    if (password !== confirmPassword) {
                        alert('❌ Passwords do not match!');
                        return;
                    }
                    
                    if (password.length < 6) {
                        alert('❌ Password must be at least 6 characters long!');
                        return;
                    }
                    
                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.textContent = 'Creating Account...';
                    submitBtn.disabled = true;
                    
                    const result = await firebaseService.register(email, password, fullName);
                    
                    if (result.success) {
                        alert('✅ Account created successfully! Welcome to StockFlow Pro!');
                        showDashboard(result.user);
                    } else {
                        alert('❌ Registration failed: ' + result.error);
                    }
                    
                    submitBtn.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <line x1="19" y1="8" x2="19" y2="14"></line>
                            <line x1="22" y1="11" x2="16" y2="11"></line>
                        </svg>
                        Create Account
                    `;
                    submitBtn.disabled = false;
                });
            }

            // Navigation buttons
            if (showRegisterBtn) {
                showRegisterBtn.addEventListener('click', showRegisterPage);
            }
            
            if (showLoginBtn) {
                showLoginBtn.addEventListener('click', showLoginPage);
            }

            window.logout = logout;

            // Check auth state on page load - FIXED: Check if auth exists
            if (typeof auth !== 'undefined' && auth) {
                auth.onAuthStateChanged((user) => {
                    if (user) {
                        showDashboard(user);
                    } else {
                        showLoginPage();
                    }
                });
            } else {
                // If auth not loaded yet, show login page by default
                showLoginPage();
                
                // Wait for Firebase to load then check auth
                const waitForFirebase = setInterval(() => {
                    if (typeof auth !== 'undefined' && auth) {
                        clearInterval(waitForFirebase);
                        auth.onAuthStateChanged((user) => {
                            if (user) {
                                showDashboard(user);
                            } else {
                                showLoginPage();
                            }
                        });
                    }
                }, 100);
            }
        }

        // Settings Management
        function initSettings() {
            const saveBtn = document.getElementById('saveSettings');
            const testBtn = document.getElementById('testWhatsAppNow');

            if (saveBtn) {
                saveBtn.addEventListener('click', saveSettingsConfig);
            }

            if (testBtn) {
                testBtn.addEventListener('click', async () => {
                    if (whatsAppService) {
                        const success = await whatsAppService.sendTestReport();
                        if (success) {
                            addToActivityFeed('accent', 'WhatsApp Test Report Sent', 'Successfully delivered to configured recipients • just now');
                        }
                    } else {
                        alert('Please configure WhatsApp settings first!');
                    }
                });
            }
        }

        function saveSettingsConfig() {
            const config = {
                token: document.getElementById('settingsFontteKey').value,
                admin1Phone: document.getElementById('settingsAdmin1').value,
                admin2Phone: document.getElementById('settingsAdmin2').value,
                groupId: document.getElementById('settingsGroupId').value,
                autoReport: document.getElementById('autoReportToggle').checked,
                autoTime: document.getElementById('autoReportTime').value
            };

            localStorage.setItem('settingsConfig', JSON.stringify(config));
            
            // Also update WhatsApp service config
            if (whatsAppService) {
                whatsAppService.saveConfig({
                    token: config.token,
                    admin1Phone: config.admin1Phone,
                    admin2Phone: config.admin2Phone,
                    groupId: config.groupId
                });

                whatsAppService.schedules.dailyReport.active = config.autoReport;
                whatsAppService.schedules.dailyReport.time = config.autoTime;
                whatsAppService.saveSchedules();
            }

            alert('✅ Settings saved successfully!');
            addToActivityFeed('primary', 'Settings Updated', 'WhatsApp configuration and preferences saved • just now');
        }

        function loadSettingsConfig() {
            const config = JSON.parse(localStorage.getItem('settingsConfig') || '{}');

            const fields = {
                'settingsFontteKey': config.token || '',
                'settingsAdmin1': config.admin1Phone || '',
                'settingsAdmin2': config.admin2Phone || '',
                'settingsGroupId': config.groupId || '',
                'autoReportTime': config.autoTime || '18:00'
            };

            Object.entries(fields).forEach(([id, value]) => {
                const field = document.getElementById(id);
                if (field) field.value = value;
            });

            const autoToggle = document.getElementById('autoReportToggle');
            if (autoToggle) autoToggle.checked = config.autoReport !== false;
        }

        // Enhanced Product Modal Management
        function initProductModal() {
            const addProductBtn = document.getElementById('addProductBtn');
            const addProductFab = document.getElementById('addProductFab');
            const modal = document.getElementById('addProductModal');
            const closeModal = document.getElementById('closeModal');
            const cancelAdd = document.getElementById('cancelAdd');
            const addProductForm = document.getElementById('addProductForm');
            const successMessage = document.getElementById('successMessage');

            function openModal() {
                modal.classList.add('open');
                document.body.style.overflow = 'hidden';
            }

            function closeModalHandler() {
                modal.classList.remove('open');
                document.body.style.overflow = 'auto';
                addProductForm.reset();
                successMessage.style.display = 'none';
            }

            if (addProductBtn) addProductBtn.addEventListener('click', openModal);
            if (addProductFab) addProductFab.addEventListener('click', openModal);
            if (closeModal) closeModal.addEventListener('click', closeModalHandler);
            if (cancelAdd) cancelAdd.addEventListener('click', closeModalHandler);

            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) closeModalHandler();
                });
            }

            if (addProductForm) {
                addProductForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    
                    const formData = new FormData(addProductForm);
                    const productData = {
                        name: formData.get('productName'),
                        code: formData.get('productCode'),
                        category: formData.get('category'),
                        initialStock: parseInt(formData.get('initialStock'))
                    };

                    addProductToTable(productData);
                    successMessage.style.display = 'block';
                    addProductForm.reset();
                    
                    setTimeout(() => {
                        closeModalHandler();
                    }, 2000);
                });
            }
        }

        // Enhanced Stock Forms Management
        function initStockForms() {
            initStockInForm();
            initStockOutForm();
            populateProductDropdowns();
        }

        // Firebase Data Management
        async function loadFirebaseData() {
            if (!firebaseService.user) return;
            
            try {
                // Load products from Firebase
                const products = await firebaseService.loadProducts();
                
                // Update inventory table
                const tableBody = document.getElementById('inventoryTableBody');
                if (tableBody && products.length > 0) {
                    // Clear existing demo data
                    tableBody.innerHTML = '';
                    
                    products.forEach(product => {
                        const row = document.createElement('tr');
                        const status = getStockStatus(product.currentStock);
                        
                        row.innerHTML = `
                            <td>${product.name}</td>
                            <td>${product.code}</td>
                            <td>${product.category}</td>
                            <td>${product.initialStock}</td>
                            <td>${product.stockIn || 0}</td>
                            <td>${product.stockOut || 0}</td>
                            <td>${product.currentStock}</td>
                            <td><span class="status-badge ${status.class}"><span class="status-dot"></span>${status.text}</span></td>
                            <td>
                                <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                            </td>
                        `;
                        
                        tableBody.appendChild(row);
                    });
                }
                
                // Update dropdowns
                populateProductDropdowns();
                
                // Update dashboard stats
                updateDashboardStats();
                
            } catch (error) {
                console.error('Error loading Firebase data:', error);
            }
        }

        function getStockStatus(stock) {
            if (stock <= 5) {
                return { class: 'danger', text: 'Critical' };
            } else if (stock <= 20) {
                return { class: 'warning', text: 'Low' };
            } else {
                return { class: 'safe', text: 'Safe' };
            }
        }

        // Get all products (Firebase + demo for now) - FIXED ASYNC ISSUE
        async function getAllProducts() {
            if (!firebaseService || !firebaseService.user) {
                // Return demo data if not logged in
                return [
                    { code: 'IP15PM001', name: 'iPhone 15 Pro Max', current: 186 },
                    { code: 'MBP24M3002', name: 'MacBook Pro M3', current: 93 },
                    { code: 'APP24G3003', name: 'AirPods Pro Gen 3', current: 15 },
                    { code: 'MKB24004', name: 'Magic Keyboard', current: 3 },
                    { code: 'SD24005', name: 'Studio Display', current: 59 }
                ];
            }
            
            try {
                const firebaseProducts = await firebaseService.loadProducts();
                const products = firebaseProducts.map(p => ({
                    id: p.id,
                    code: p.code,
                    name: p.name,
                    current: p.currentStock
                }));
                
                // Add demo products if no Firebase products
                if (products.length === 0) {
                    return [
                        { code: 'IP15PM001', name: 'iPhone 15 Pro Max', current: 186 },
                        { code: 'MBP24M3002', name: 'MacBook Pro M3', current: 93 },
                        { code: 'APP24G3003', name: 'AirPods Pro Gen 3', current: 15 },
                        { code: 'MKB24004', name: 'Magic Keyboard', current: 3 },
                        { code: 'SD24005', name: 'Studio Display', current: 59 }
                    ];
                }
                
                return products;
            } catch (error) {
                console.error('Error getting products:', error);
                return [
                    { code: 'IP15PM001', name: 'iPhone 15 Pro Max', current: 186 },
                    { code: 'MBP24M3002', name: 'MacBook Pro M3', current: 93 },
                    { code: 'APP24G3003', name: 'AirPods Pro Gen 3', current: 15 },
                    { code: 'MKB24004', name: 'Magic Keyboard', current: 3 },
                    { code: 'SD24005', name: 'Studio Display', current: 59 }
                ];
            }
        }

        // Populate product dropdowns - FIXED ASYNC HANDLING
        async function populateProductDropdowns() {
            const stockInSelect = document.getElementById('stockInProductCode');
            const stockOutSelect = document.getElementById('stockOutProductCode');
            
            try {
                const products = await getAllProducts();

                if (stockInSelect) {
                    stockInSelect.innerHTML = '<option value="">Select product SKU</option>';
                    products.forEach(product => {
                        const option = document.createElement('option');
                        option.value = product.code;
                        option.textContent = `${product.code} - ${product.name}`;
                        option.dataset.name = product.name;
                        option.dataset.current = product.current;
                        stockInSelect.appendChild(option);
                    });
                }

                if (stockOutSelect) {
                    stockOutSelect.innerHTML = '<option value="">Select product SKU</option>';
                    products.forEach(product => {
                        const option = document.createElement('option');
                        option.value = product.code;
                        option.textContent = `${product.code} - ${product.name} (Stock: ${product.current})`;
                        option.dataset.name = product.name;
                        option.dataset.current = product.current;
                        stockOutSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error populating dropdowns:', error);
            }
        }

        // Stock In Form
        function initStockInForm() {
            const form = document.getElementById('stockInForm');
            const codeSelect = document.getElementById('stockInProductCode');
            const nameInput = document.getElementById('stockInProductName');

            if (codeSelect && nameInput) {
                codeSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.dataset.name) {
                        nameInput.value = selectedOption.dataset.name;
                    } else {
                        nameInput.value = '';
                    }
                });
            }

            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const code = document.getElementById('stockInProductCode').value;
                    const name = document.getElementById('stockInProductName').value;
                    const quantity = parseInt(document.getElementById('stockInQuantity').value);

                    if (!code || !quantity) {
                        alert('Please fill all required fields');
                        return;
                    }

                    addStockInTransaction(code, name, quantity);
                    updateInventoryStock(code, quantity, 'in');
                    updateDashboardStockIn();
                    
                    form.reset();
                    nameInput.value = '';
                    
                    alert('✅ Stock In recorded successfully!');
                });
            }
        }

        // Stock Out Form
        function initStockOutForm() {
            const form = document.getElementById('stockOutForm');
            const codeSelect = document.getElementById('stockOutProductCode');
            const nameInput = document.getElementById('stockOutProductName');

            if (codeSelect && nameInput) {
                codeSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.dataset.name) {
                        nameInput.value = selectedOption.dataset.name;
                    } else {
                        nameInput.value = '';
                    }
                });
            }

            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const code = document.getElementById('stockOutProductCode').value;
                    const name = document.getElementById('stockOutProductName').value;
                    const quantity = parseInt(document.getElementById('stockOutQuantity').value);

                    if (!code || !quantity) {
                        alert('Please fill all required fields');
                        return;
                    }

                    const selectedOption = codeSelect.options[codeSelect.selectedIndex];
                    const currentStock = parseInt(selectedOption.dataset.current);
                    
                    if (quantity > currentStock) {
                        alert(`❌ Insufficient stock! Available: ${currentStock} units`);
                        return;
                    }

                    addStockOutTransaction(code, name, quantity);
                    updateInventoryStock(code, quantity, 'out');
                    updateDashboardStockOut();
                    
                    form.reset();
                    nameInput.value = '';
                    populateProductDropdowns();
                    
                    alert('✅ Stock Out recorded successfully!');
                });
            }
        }

        // Add stock in transaction to table
        function addStockInTransaction(code, name, quantity) {
            const tableBody = document.getElementById('stockInTableBody');
            if (!tableBody) return;

            const now = new Date();
            const dateStr = now.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric'
            }) + ' ' + now.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dateStr}</td>
                <td>${code}</td>
                <td>${name}</td>
                <td data-type="stock-in">+${quantity}</td>
                <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
            `;
            
            tableBody.insertBefore(row, tableBody.firstChild);
            addToActivityFeed('success', `Stock In: ${name}`, `+${quantity} units • SKU: ${code} • just now`);
        }

        // Add stock out transaction to table
        function addStockOutTransaction(code, name, quantity) {
            const tableBody = document.getElementById('stockOutTableBody');
            if (!tableBody) return;

            const now = new Date();
            const dateStr = now.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric'
            }) + ' ' + now.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dateStr}</td>
                <td>${code}</td>
                <td>${name}</td>
                <td data-type="stock-out">-${quantity}</td>
                <td><span class="status-badge safe"><span class="status-dot"></span>Completed</span></td>
            `;
            
            tableBody.insertBefore(row, tableBody.firstChild);
            addToActivityFeed('warning', `Stock Out: ${name}`, `-${quantity} units • SKU: ${code} • just now`);
        }

        // Update inventory stock in table
        function updateInventoryStock(code, quantity, type) {
            const inventoryTable = document.getElementById('inventoryTableBody');
            if (!inventoryTable) return;

            const rows = inventoryTable.querySelectorAll('tr');
            rows.forEach(row => {
                const rowCode = row.cells[1].textContent;
                if (rowCode === code) {
                    const inCell = row.cells[4];
                    const outCell = row.cells[5];
                    const currentCell = row.cells[6];
                    
                    let currentIn = parseInt(inCell.textContent);
                    let currentOut = parseInt(outCell.textContent);
                    let currentStock = parseInt(currentCell.textContent);
                    
                    if (type === 'in') {
                        currentIn += quantity;
                        currentStock += quantity;
                    } else {
                        currentOut += quantity;
                        currentStock -= quantity;
                    }
                    
                    inCell.textContent = currentIn;
                    outCell.textContent = currentOut;
                    currentCell.textContent = currentStock;
                    
                    const statusCell = row.cells[7];
                    updateStatusBadge(statusCell, currentStock, row.cells[0].textContent, code);
                    
                    row.style.background = type === 'in' ? 
                        'linear-gradient(135deg, rgba(0, 212, 170, 0.15) 0%, rgba(0, 212, 170, 0.08) 100%)' : 
                        'linear-gradient(135deg, rgba(255, 184, 0, 0.15) 0%, rgba(255, 184, 0, 0.08) 100%)';
                    setTimeout(() => {
                        row.style.background = '';
                    }, 3000);
                }
            });
        }

        // Update status badge based on stock level
        function updateStatusBadge(statusCell, stock, productName, sku) {
            let badgeClass = 'safe';
            let statusText = 'Safe';
            
            if (stock <= 5) {
                badgeClass = 'danger';
                statusText = 'Critical';
                
                addToActivityFeed('error', `Critical Alert: ${productName}`, `Only ${stock} units remaining • SKU: ${sku} • just now`);
                
                if (whatsAppService && whatsAppService.schedules.criticalAlert.active) {
                    setTimeout(() => whatsAppService.sendCriticalAlert(), 1000);
                }
            } else if (stock <= 20) {
                badgeClass = 'warning';
                statusText = 'Low';
            }
            
            statusCell.innerHTML = `<span class="status-badge ${badgeClass}"><span class="status-dot"></span>${statusText}</span>`;
        }
        
        // Function to add activity to the activity feed
        function addToActivityFeed(type, title, meta) {
            const activityList = document.querySelector('#activityContent .activity-list');
            if (!activityList) return;
            
            const gradientTypes = {
                'success': 'var(--gradient-success)',
                'warning': 'var(--gradient-warning)',
                'error': 'var(--gradient-error)',
                'primary': 'var(--gradient-primary)',
                'accent': 'var(--gradient-accent)'
            };
            
            const iconTypes = {
                'success': '<path d="M7 17l6-6 6 6"></path><path d="M13 11v10"></path>',
                'warning': '<path d="M17 7l-6 6-6-6"></path><path d="M11 13V3"></path>',
                'error': '<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>',
                'primary': '<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>',
                'accent': '<line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22,2 15,22 11,13 2,9"></polygon>'
            };
            
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            activityItem.style.opacity = '0';
            activityItem.style.transform = 'translateY(-20px)';
            
            activityItem.innerHTML = `
                <div class="activity-icon" style="background: ${gradientTypes[type]};">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        ${iconTypes[type]}
                    </svg>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${title}</div>
                    <div class="activity-meta">${meta}</div>
                </div>
            `;
            
            activityList.insertBefore(activityItem, activityList.firstChild);
            
            setTimeout(() => {
                activityItem.style.transition = 'all 0.6s var(--ease-spring)';
                activityItem.style.opacity = '1';
                activityItem.style.transform = 'translateY(0)';
            }, 10);
        }

        // Update dashboard counters
        function updateDashboardStockIn() {
            const stockInEl = document.getElementById('stockIn');
            if (stockInEl) {
                const current = parseInt(stockInEl.textContent);
                stockInEl.textContent = current + 1;
            }
        }

        function updateDashboardStockOut() {
            const stockOutEl = document.getElementById('stockOut');
            if (stockOutEl) {
                const current = parseInt(stockOutEl.textContent);
                stockOutEl.textContent = current + 1;
            }
        }

        // Add product to inventory table
        function addProductToTable(product) {
            const tableBody = document.getElementById('inventoryTableBody');
            if (!tableBody) return;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.name}</td>
                <td>${product.code}</td>
                <td>${product.category}</td>
                <td>${product.initialStock}</td>
                <td>0</td>
                <td>0</td>
                <td>${product.initialStock}</td>
                <td><span class="status-badge safe"><span class="status-dot"></span>Safe</span></td>
                <td>
                    <button class="btn btn-ghost" style="padding: var(--space-2) var(--space-3); font-size: 13px;">Edit</button>
                </td>
            `;
            
            row.style.opacity = '0';
            row.style.transform = 'translateY(-20px)';
            row.classList.add('slide-in');
            tableBody.appendChild(row);
            
            setTimeout(() => {
                row.style.transition = 'all 0.6s var(--ease-spring)';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, 10);

            saveProductToStorage(product);
            updateDashboardStats();
            
            addToActivityFeed('primary', `New Product Added: ${product.name}`, `${product.initialStock} initial stock • SKU: ${product.code} • just now`);
            
            setTimeout(populateProductDropdowns, 500);
        }

        function saveProductToStorage(product) {
            let products = JSON.parse(localStorage.getItem('products') || '[]');
            products.push({
                ...product,
                id: Date.now(),
                stockIn: 0,
                stockOut: 0,
                currentStock: product.initialStock,
                code: product.code,
                name: product.name
            });
            localStorage.setItem('products', JSON.stringify(products));
        }

        function initProductSearch() {
            const searchInput = document.getElementById('productSearch');
            if (!searchInput) return;

            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const tableRows = document.querySelectorAll('#inventoryTableBody tr');
                
                tableRows.forEach(row => {
                    const productName = row.cells[0].textContent.toLowerCase();
                    const productCode = row.cells[1].textContent.toLowerCase();
                    const category = row.cells[2].textContent.toLowerCase();
                    
                    if (productName.includes(searchTerm) || 
                        productCode.includes(searchTerm) || 
                        category.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        function updateDashboardStats() {
            const products = JSON.parse(localStorage.getItem('products') || '[]');
            const totalProducts = products.length + 5;
            
            const totalProductsEl = document.getElementById('totalProducts');
            if (totalProductsEl) {
                totalProductsEl.textContent = totalProducts;
            }
        }

        // WhatsApp Configuration Management
        function initWhatsAppConfig() {
            const saveBtn = document.getElementById('saveWhatsAppConfig');
            const sendTestBtn = document.getElementById('sendTestReport');
            
            if (saveBtn) {
                saveBtn.addEventListener('click', saveWhatsAppConfig);
            }
            
            if (sendTestBtn) {
                sendTestBtn.addEventListener('click', async () => {
                    if (whatsAppService) {
                        const success = await whatsAppService.sendTestReport();
                        if (success) {
                            addToActivityFeed('accent', 'WhatsApp Test Report Sent', 'Successfully delivered to configured recipients • just now');
                        }
                    } else {
                        alert('Please configure WhatsApp settings first!');
                    }
                });
            }
        }

        function saveWhatsAppConfig() {
            const config = {
                token: document.getElementById('fontteToken').value,
                admin1Phone: document.getElementById('admin1Phone').value,
                admin2Phone: document.getElementById('admin2Phone').value,
                groupId: document.getElementById('groupId').value
            };
            
            if (!config.token) {
                alert('Please enter your Fonnte token!');
                return;
            }
            
            if (whatsAppService) {
                whatsAppService.saveConfig(config);
                alert('✅ WhatsApp configuration saved successfully!');
                addToActivityFeed('accent', 'WhatsApp Configuration Updated', 'Settings saved and ready for automated reports • just now');
            }
        }

        function loadWhatsAppConfig() {
            if (!whatsAppService) return;
            
            const config = whatsAppService.config;
            
            const fields = {
                'fontteToken': config.token || '',
                'admin1Phone': config.admin1Phone || '',
                'admin2Phone': config.admin2Phone || '',
                'groupId': config.groupId || ''
            };

            Object.entries(fields).forEach(([id, value]) => {
                const field = document.getElementById(id);
                if (field) field.value = value;
            });
        }

        // CHART FUNCTIONS - FIXED AND ENHANCED
        function waitForChart(callback) {
            if (typeof Chart !== 'undefined') {
                callback();
            } else {
                setTimeout(() => waitForChart(callback), 100);
            }
        }

        function initCharts() {
            waitForChart(() => {
                console.log('Initializing dashboard chart...');
                
                const stockCtx = document.getElementById('stockChart');
                if (!stockCtx) {
                    console.error('stockChart canvas not found');
                    return;
                }

                // Destroy existing chart if exists
                if (stockChartInstance) {
                    stockChartInstance.destroy();
                }

                try {
                    stockChartInstance = new Chart(stockCtx, {
                        type: 'line',
                        data: {
                            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'],
                            datasets: [{
                                label: 'Stock In',
                                data: [145, 162, 138, 147, 143, 171, 147],
                                borderColor: '#00d4aa',
                                backgroundColor: 'rgba(0, 212, 170, 0.1)',
                                tension: 0.4,
                                fill: true,
                                borderWidth: 3,
                                pointBackgroundColor: '#00d4aa',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 6,
                                pointHoverRadius: 8
                            }, {
                                label: 'Stock Out',
                                data: [95, 101, 112, 89, 125, 95, 89],
                                borderColor: '#ffb800',
                                backgroundColor: 'rgba(255, 184, 0, 0.1)',
                                tension: 0.4,
                                fill: true,
                                borderWidth: 3,
                                pointBackgroundColor: '#ffb800',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 6,
                                pointHoverRadius: 8
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top',
                                    labels: {
                                        padding: 25,
                                        usePointStyle: true,
                                        font: {
                                            weight: 700,
                                            size: 14,
                                            family: 'Inter'
                                        }
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                                    titleColor: '#f8fafc',
                                    bodyColor: '#e2e8f0',
                                    borderColor: 'rgba(102, 126, 234, 0.5)',
                                    borderWidth: 1,
                                    cornerRadius: 12,
                                    padding: 16,
                                    titleFont: {
                                        weight: 700,
                                        family: 'Inter'
                                    },
                                    bodyFont: {
                                        family: 'Inter'
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(148, 163, 184, 0.1)',
                                        drawBorder: false
                                    },
                                    ticks: {
                                        font: {
                                            weight: 600,
                                            family: 'Inter'
                                        },
                                        color: '#64748b'
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            weight: 600,
                                            family: 'Inter'
                                        },
                                        color: '#64748b'
                                    }
                                }
                            }
                        }
                    });
                    console.log('Dashboard chart initialized successfully');
                } catch (error) {
                    console.error('Error initializing dashboard chart:', error);
                }
            });
        }

        function initReportsCharts() {
            waitForChart(() => {
                console.log('Initializing reports chart...');
                
                const topProductsCtx = document.getElementById('topProductsChart');
                if (!topProductsCtx) {
                    console.error('topProductsChart canvas not found');
                    return;
                }

                // Destroy existing chart if exists
                if (topProductsChartInstance) {
                    topProductsChartInstance.destroy();
                }

                try {
                    topProductsChartInstance = new Chart(topProductsCtx, {
                        type: 'bar',
                        data: {
                            labels: ['iPhone 15 Pro Max', 'MacBook Pro M3', 'AirPods Pro Gen 3', 'Studio Display', 'Magic Keyboard'],
                            datasets: [{
                                label: 'Units Sold',
                                data: [158, 89, 165, 19, 35],
                                backgroundColor: [
                                    'rgba(102, 126, 234, 0.8)',
                                    'rgba(0, 212, 170, 0.8)',
                                    'rgba(255, 184, 0, 0.8)',
                                    'rgba(255, 107, 157, 0.8)',
                                    'rgba(255, 87, 87, 0.8)'
                                ],
                                borderColor: [
                                    'rgba(102, 126, 234, 1)',
                                    'rgba(0, 212, 170, 1)',
                                    'rgba(255, 184, 0, 1)',
                                    'rgba(255, 107, 157, 1)',
                                    'rgba(255, 87, 87, 1)'
                                ],
                                borderWidth: 2,
                                borderRadius: 8
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                                    titleColor: '#f8fafc',
                                    bodyColor: '#e2e8f0',
                                    borderColor: 'rgba(102, 126, 234, 0.5)',
                                    borderWidth: 1,
                                    cornerRadius: 12,
                                    padding: 16,
                                    titleFont: {
                                        weight: 700,
                                        family: 'Inter'
                                    },
                                    bodyFont: {
                                        family: 'Inter'
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(148, 163, 184, 0.1)',
                                        drawBorder: false
                                    },
                                    ticks: {
                                        font: {
                                            weight: 600,
                                            family: 'Inter'
                                        },
                                        color: '#64748b'
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            weight: 600,
                                            family: 'Inter'
                                        },
                                        color: '#64748b',
                                        maxRotation: 45
                                    }
                                }
                            }
                        }
                    });
                    console.log('Reports chart initialized successfully');
                } catch (error) {
                    console.error('Error initializing reports chart:', error);
                }
            });
        }

        function animateCounters() {
            const counters = [
                { element: document.getElementById('totalProducts'), target: 286 },
                { element: document.getElementById('stockIn'), target: 147 },
                { element: document.getElementById('stockOut'), target: 89 },
                { element: document.getElementById('criticalStock'), target: 7 }
            ];

            counters.forEach(counter => {
                if (counter.element) {
                    let current = 0;
                    const increment = counter.target / 80;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= counter.target) {
                            current = counter.target;
                            clearInterval(timer);
                        }
                        counter.element.textContent = Math.floor(current).toLocaleString();
                    }, 25);
                }
            });
        }

        function initDashboard() {
            console.log('Initializing dashboard...');
            animateCounters();
            initCharts();
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM Content Loaded');
            
            // FIXED: Wait for Firebase to load and initialize before creating service
            const waitForFirebaseInit = () => {
                if (typeof auth !== 'undefined' && auth && typeof db !== 'undefined' && db) {
                    console.log('Firebase ready, initializing service...');
                    // Initialize Firebase service
                    firebaseService = new FirebaseService();
                } else {
                    console.log('Waiting for Firebase to initialize...');
                    setTimeout(waitForFirebaseInit, 100);
                }
            };
            
            waitForFirebaseInit();
            
            initTheme();
            initSidebar();
            initNavigation();
            initLogin();
            initProductModal();
            initProductSearch();
            initStockForms();
            initWhatsAppConfig();
            initSettings();
            
            // Chart.js handling with multiple fallbacks
            const initChartsWithFallback = () => {
                if (typeof Chart !== 'undefined') {
                    console.log('Chart.js already loaded');
                    return;
                }
                
                console.log('Chart.js not loaded, adding fallbacks...');
                
                // Try multiple CDN sources
                const chartSources = [
                    'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js',
                    'https://unpkg.com/chart.js@3.9.1/dist/chart.min.js'
                ];
                
                let sourceIndex = 0;
                
                const loadNextSource = () => {
                    if (sourceIndex >= chartSources.length) {
                        console.error('All Chart.js sources failed to load');
                        return;
                    }
                    
                    const script = document.createElement('script');
                    script.src = chartSources[sourceIndex];
                    script.onload = () => {
                        console.log(`Chart.js loaded from source ${sourceIndex + 1}`);
                        // FIXED: Check if auth exists before using it
                        if (typeof auth !== 'undefined' && auth) {
                            auth.onAuthStateChanged((user) => {
                                if (user) {
                                    setTimeout(() => {
                                        initCharts();
                                        initReportsCharts();
                                    }, 500);
                                }
                            });
                        }
                    };
                    script.onerror = () => {
                        console.log(`Chart.js source ${sourceIndex + 1} failed, trying next...`);
                        sourceIndex++;
                        loadNextSource();
                    };
                    document.head.appendChild(script);
                };
                
                loadNextSource();
            };
            
            // Check if Chart.js is already loaded, if not load it
            if (typeof Chart === 'undefined') {
                initChartsWithFallback();
            }
        });

        // Final chart initialization on window load
        window.addEventListener('load', () => {
            console.log('Window fully loaded');
            
            setTimeout(() => {
                // FIXED: Check if auth exists before using it
                if (typeof auth !== 'undefined' && auth) {
                    auth.onAuthStateChanged((user) => {
                        if (user) {
                            console.log('Re-initializing charts after window load...');
                            
                            const dashboardContent = document.getElementById('dashboardContent');
                            if (dashboardContent && dashboardContent.classList.contains('active')) {
                                if (typeof Chart !== 'undefined') {
                                    initCharts();
                                } else {
                                    console.log('Chart.js still not available after window load');
                                }
                            }
                        }
                    });
                } else {
                    console.log('Firebase auth not yet initialized');
                }
            }, 1000);
        });
    </script>
</body>
</html>